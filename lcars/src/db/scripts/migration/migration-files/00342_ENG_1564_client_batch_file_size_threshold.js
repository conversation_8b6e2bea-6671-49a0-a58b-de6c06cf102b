export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
    -- Add client batch page count threshold setting
    INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_number)
    VALUES ('client_batch_page_count_threshold', 'Batch Page Count Threshold', 'Page count threshold for when to use parallel processing during document ingestion', 'number', true, true, 100) 
    ON CONFLICT (name) DO NOTHING;
`;

export const down = `
    -- Remove the setting definition
    DELETE FROM tribble.setting WHERE name = 'client_batch_page_count_threshold';
`;
