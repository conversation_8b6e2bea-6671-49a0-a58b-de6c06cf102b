import { ContentDetailId } from '@tribble/tribble-db/clients/ContentDetail';
import { DocumentId } from '@tribble/tribble-db/clients/Document';
import { DB, TRX, getDB, queryIntegration } from '@tribble/tribble-db/db_ops';
import {
  Document,
  DocumentType,
  DocumentTypePriority,
  Embedding,
  FileWithMetadata,
  MetadataFilterType,
  MetadataFilterValue,
  NewMetadataFilterValue,
} from 'src/model/model';
import { v4 as uuidv4 } from 'uuid';
import { getIntegrationSchema } from '../integrations/airbyte/utils';
import { getOrCreateDocTypeIdByName } from '../utils/docs';
const { query, getClient, getCustomSchema } = require('./index');
const { constants } = require('../constants');

//Label, document_type_id, privacy
export async function updateDocumentAttributes(
  schema: string,
  docId: number,
  attributes: Partial<Document>,
): Promise<boolean> {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_DOC}
    SET label = $1,
    description = $2,
    document_type_id = $3,
    privacy = $4,
    expiry_date = $5
    WHERE id = $6
    `;
  try {
    const result = await query(queryString, [
      attributes.label,
      attributes.description,
      attributes.document_type_id,
      attributes.privacy,
      attributes.expiry_date,
      docId,
    ]);
    if (!result.rowCount || result.rowCount != 1) {
      success = false;
    }
  } catch (err) {
    console.error(`[db.updateDocumentAttributes] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateDocumentLabel(
  schema: string,
  document_id: number,
  label: string,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_DOC}
    SET label = $1
    WHERE id = $2`;

  try {
    const result = await query(queryString, [label, document_id]);
  } catch (err) {
    console.error(`[db.updateDocumentLabel] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateDocumentMetadataJson(
  schema: string,
  document_ids: number[],
  key: string,
  value: any,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_DOC}
    SET metadata_json[$1] = $2::jsonb
    WHERE id = ANY($3::int[])`;

  try {
    const result = await query(queryString, [key, value, document_ids]);
  } catch (err) {
    console.error(`[db.updateDocumentMetadataJson] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateDocumentsUseForGeneration(
  schema: string,
  document_ids: number[],
  use_for_generation: boolean,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_DOC}
    SET use_for_generation = $1
    WHERE id = ANY($2)`;

  try {
    const result = await query(queryString, [use_for_generation, document_ids]);
  } catch (err) {
    console.error(`[db.updateDocumentUseForGeneration] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateDocumentUuid(
  schema: string,
  document_id: number,
  uuid: string,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_DOC}
    SET uuid = $1
    WHERE id = $2`;

  try {
    const result = await query(queryString, [uuid, document_id]);
  } catch (err) {
    console.error(`[db.updateDocumentUuid] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

// Currently just used by the Manage Tags (Multiple Sites) workflow.
// That only requires the id and metadata_json fields.
// (Add more to this as needed in future.)
export async function getDocsByFileNames(schema: string, fileNames: string[]) {
  const queryString = `
    SELECT id, metadata_json
    FROM ${schema}.${constants.TABLE_DOC}
    WHERE file_name = ANY($1::text[])
    AND status != 'deleted'
  `;

  const res = await query(queryString, [fileNames]);

  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

export async function getWebDocsByPath(
  schema: string,
  filter = '',
  hostname?: string,
  path?: string,
  count?: boolean,
  statuses?: string[],
  uploadedBy?: number[],
  searchValue?: string,
  maxDate?: string,
  minDate?: string,
): Promise<Partial<Document>[]> {
  const hasFilter = filter != '';
  const queryParams = [];

  let queryString = `WITH unique_path_segments AS (
    SELECT
    CASE
      WHEN d.metadata_json IS NOT NULL AND d.metadata_json::text != '{}' THEN d.metadata_json->'pathSegments'->>0
      WHEN d.metadata_json IS NULL OR d.metadata_json::text = '{}' THEN
      CASE
          WHEN POSITION('/' IN SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3)) > 0 THEN
              SPLIT_PART(SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3), '/', 2)
          ELSE ''
      END
    END AS first_level_path,
    CASE
      WHEN d.metadata_json IS NOT NULL AND d.metadata_json::text != '{}' THEN d.metadata_json->>'hostname'
      WHEN d.metadata_json IS NULL OR d.metadata_json::text = '{}' THEN SPLIT_PART(SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3), '/', 1)
    END AS hostname, d.id
    FROM ${schema}.${constants.TABLE_DOC} d
    WHERE d.use_for_generation = true
    AND d.status != 'deleted'
  )`;
  if (hostname) {
    if (count) {
      queryString += `SELECT COUNT(*)`;
    } else {
      queryString += `
      SELECT`;
    }
  } else {
    queryString += `
    SELECT DISTINCT ON (ups.hostname, ups.first_level_path)`;
  }
  if (!count) {
    queryString += `
      d.id,
      ups.hostname,
      ups.first_level_path as pathSegment,
      d.uuid,
      d.file_name,
      d.label,
      d.document_revision,
      d.cloud_version_id,
      d.prior_version_document_id,
      d.created_date,
      d.created_by_id,
      users.name as created_by_name,
      CASE
        WHEN d.status = '${constants.DOC_STATUS_ACTIVE}' THEN d.status
        WHEN j.status = '${constants.JOB_STATUS_ERROR}' THEN j.status
        ELSE d.status
      END AS status,
      d.document_type_id,
      dt.name as type,
      dt.is_rfp,
      dt.is_website,
      d.privacy,
      d.use_for_generation,
      d2.id as updated_document_id,
      d2.status as updated_document_status,
      d.expiry_date,
      CASE
        WHEN j.status = '${constants.JOB_STATUS_PROCESSING}' THEN COALESCE(jl.data, '{}'::jsonb)
        WHEN d.status = '${constants.DOC_STATUS_PROCESSING}' THEN COALESCE(jl.data, '{}'::jsonb)
        ELSE NULL
      END AS job_log_status
      `;
  }
  queryString += ` FROM ${schema}.${constants.TABLE_DOC} d
  JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} as users
    ON d.created_by_id = users.id
  JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} as dt
    ON d.document_type_id = dt.id
    AND dt.name IN (SELECT name FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE} WHERE is_website = true)
  LEFT JOIN ${schema}.${constants.TABLE_DOC} as d2
    ON d2.prior_version_document_id = d.id
    AND d2.status in ('${constants.DOC_STATUS_PROCESSING}', '${constants.DOC_STATUS_ACTIVE}')
  LEFT JOIN ${schema}.${constants.TABLE_JOB} as j
    ON j.id = d.job_id
  JOIN unique_path_segments ups
    ON ups.id = d.id
  LEFT JOIN LATERAL (
    SELECT data
    FROM ${schema}.${constants.TABLE_JOB_LOG} jl
    WHERE jl.job_id = d.job_id
    AND jl.type = 'status'
    ORDER BY id DESC
    LIMIT 1
  ) jl ON TRUE
  `;

  if (statuses && statuses.length > 0) {
    queryString += `
    WHERE d.status IN (${statuses.map((status) => `'${status}'`).join(',')})`;
  } else {
    queryString += `
    WHERE d.status != 'deleted'`;
  }

  if (uploadedBy && uploadedBy.length > 0) {
    queryString += `
    AND d.created_by_id IN (${uploadedBy.join(',')})`;
  }

  if (searchValue) {
    queryString += `AND (d.file_name ILIKE $1 OR d.label ILIKE $1)`;
    queryParams.push(`%${searchValue}%`);
  }

  if (hasFilter) {
    queryString += `
    AND ${filter}`;
  }

  if (maxDate) {
    queryString += `
    AND d.created_date <= '${maxDate}'`;
  }
  if (minDate) {
    queryString += `
    AND d.created_date >= '${minDate}'`;
  }

  if (hostname) {
    queryString += `
    AND ups.hostname LIKE '${hostname}%'`;
    if (path !== null && path !== '') {
      queryString += `
      AND ups.first_level_path = '${path}'`;
    } else {
      queryString += `
      AND (ups.first_level_path = '' or ups.first_level_path is null)`;
    }
  }

  if (!count) {
    queryString += `
    ORDER BY ups.hostname, ups.first_level_path, d.file_name ASC`;
  }

  try {
    const res = await query(queryString, queryParams);
    if (res && res.rows) {
      return res.rows;
    }
    return [];
  } catch (err) {
    console.error(`[db.getWebDocsByPath] ${err.message}`);
    return [];
  }
}

export const getWebDocCountAndLastCreatedByRootPath = async (
  schema: string,
  hostname: string,
  statuses: string[],
  uploadedBy: number[],
  searchValue: string,
) => {
  let queryString = `
    SELECT 
      COUNT(SPLIT_PART(SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3), '/', 2)) as count,
      MAX(d.created_date) as last_created_date,
      ARRAY_AGG(d.id) as doc_ids
    FROM ${schema}.${constants.TABLE_DOC} d
    WHERE d.use_for_generation = true`;

  if (statuses && statuses.length > 0) {
    queryString += `
    AND d.status IN (${statuses.map((status) => `'${status}'`).join(',')})`;
  }
  if (uploadedBy && uploadedBy.length > 0) {
    queryString += `
    AND d.created_by_id IN (${uploadedBy.join(',')})`;
  }
  if (searchValue) {
    queryString += `
    AND (d.file_name ILIKE '%${searchValue}%' OR d.label ILIKE '%${searchValue}%')`;
  }

  queryString += `
    AND d.status != 'deleted'
    AND SPLIT_PART(SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3), '/', 1) = $1`;

  const res = await query(queryString, [hostname]);
  if (res && res.rows) {
    return res.rows[0];
  }
  return {};
};

export const deleteWebDocsByPath = async (
  schema: string,
  userId: number,
  hostname: string,
  path?: string,
  single?: boolean,
) => {
  let client;
  try {
    client = await getClient();
    await client.query('BEGIN');

    let toDeleteQuery = `SELECT d.id
      FROM ${schema}.${constants.TABLE_DOC} d
      JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} as users ON d.created_by_id = users.id
      JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} as dt ON d.document_type_id = dt.id AND dt.name IN (SELECT name FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE} WHERE is_website = true)
      LEFT JOIN ${schema}.${constants.TABLE_DOC} as d2 ON d2.prior_version_document_id = d.id AND d2.status in ('${constants.DOC_STATUS_PROCESSING}', '${constants.DOC_STATUS_ACTIVE}')
      LEFT JOIN ${schema}.${constants.TABLE_JOB} as j ON j.id = d.job_id
      WHERE d.status != 'deleted'
      AND d.use_for_generation = true
      AND COALESCE(d.metadata_json->>'hostname', SPLIT_PART(d.file_name, '/', 3)) like '${hostname}%'`;
    if ((path !== null && path !== '') || single) {
      toDeleteQuery += `
          AND (
            (d.metadata_json IS NOT NULL AND d.metadata_json::text != '{}' AND d.metadata_json->'pathSegments'${single ? ` = '[]'` : `->>0='${path}'`})
              OR
            (d.metadata_json IS NULL OR d.metadata_json::text = '{}' AND SPLIT_PART(SPLIT_PART(SUBSTRING(d.file_name FROM POSITION('://' IN d.file_name) + 3), '?', 1), '/', 2) ${single ? 'is null' : `= '${path}'`})
          )`;
    }

    const toDeleteResult = await client.query(toDeleteQuery);

    const docIdsToDelete =
      toDeleteResult && toDeleteResult.rows && toDeleteResult.rows.length > 0
        ? toDeleteResult.rows.map((r) => r.id)
        : [];

    if (!docIdsToDelete.length) {
      throw new Error('No documents found to delete');
    }

    const deleteEmbeddings = `DELETE FROM ${schema}.${constants.TABLE_EMBEDDING}
      WHERE document_id = ANY($1::int[])
      RETURNING document_id`;

    const deletedEmbeddings = await client.query(deleteEmbeddings, [
      docIdsToDelete,
    ]);

    const softDeleteDocs = `UPDATE ${schema}.${constants.TABLE_DOC}
    SET status = '${constants.DOC_STATUS_DELETED}', deleted_date = $1, deleted_by_id = $2
    WHERE id = ANY($3::int[]);`;

    await client.query(softDeleteDocs, [new Date(), userId, docIdsToDelete]);
    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[db.getWebDocsByPath] ${err.message}`);
  } finally {
    client.release();
  }
};

export async function getWebDocIdsBySelectedPath(
  schema: string,
  selectedPath: string,
) {
  // A path either has a hostname + "/" + path, or, just a hostname + "/".
  // Configure the query accordingly.
  const selectedPathParts = selectedPath
    .split('/')
    .filter((part) => part.trim() !== '');

  let res;
  if (selectedPathParts.length == 1) {
    const queryString = `
    SELECT id
    FROM ${schema}.${constants.TABLE_DOC}
    WHERE file_name LIKE 'https://' || $1 || '%'
    AND (file_name = 'https://' || $1 || '/' OR file_name NOT LIKE 'https://' || $1 || '/%')
    AND use_for_generation = true
    AND status != 'deleted'
    `;

    res = await query(queryString, [selectedPathParts[0]]);
  } else {
    const queryString = `
    SELECT id
    FROM ${schema}.${constants.TABLE_DOC}
    WHERE file_name LIKE 'https://' || $1 || '/' || $2 || '%'
    AND use_for_generation = true
    AND status != 'deleted'
    `;

    res = await query(queryString, [
      selectedPathParts[0],
      selectedPathParts[1],
    ]);
  }

  if (res && res.rows) {
    return res.rows.map((row) => row.id);
  }
  return [];
}

export async function getDocs(
  schema: string,
  filter = '',
  includeDeleted = false,
  omitWeb = false,
  webOnly = false,
  queryParams?: any[],
): Promise<Partial<Document>[]> {
  const hasFilter = filter != '';

  let queryString = `
  SELECT
    d.id,
    d.uuid,
    d.file_name,
    d.label,
    d.document_revision,
    d.cloud_version_id,
    d.prior_version_document_id,
    d.created_date,
    d.created_by_id,
    users.name as created_by_name,
    CASE
      -- For Call Transcripts (Gong calls), if job failed but we have document + embeddings, show document status instead of error
      -- This handles cases where the job may have failed on non-critical steps but the core data (document + embeddings) exists
      WHEN j.status = '${constants.JOB_STATUS_ERROR}' AND dt.name = 'Call Transcript' AND e.embedding_count > 0 THEN d.status
      -- For other document types, you could add similar conditions here:
      -- WHEN j.status = '${constants.JOB_STATUS_ERROR}' AND dt.name = 'Other Type' AND e.embedding_count > 0 THEN d.status
      -- For all other cases, if job failed, show error status
      WHEN j.status = '${constants.JOB_STATUS_ERROR}' THEN j.status
      -- Default: show document status
      ELSE d.status
    END AS status,
    d.document_type_id,
    CASE
      WHEN dt.name = 'Spreadsheet' THEN 'Database'
      WHEN dt.name = 'Questionnaire' THEN 'Q&A Spreadsheet'
      ELSE dt.name
    END as type,
    dt.is_rfp,
    dt.is_website,
    d.privacy,
    d.use_for_generation,
    d.is_custom_table,
    d2.id as updated_document_id,
    d2.status as updated_document_status,
    d.expiry_date,
    d.metadata_json,
    CASE
      -- Hacky for now. Will update the Highspot integration to write directly to source_url field
      -- Then can remove once all Highspot files have this.
      WHEN d.id = highspot.document_id THEN 'https://www.highspot.com'
      WHEN d.id = zendesk.document_id THEN zendesk.url
      ELSE d.source_url
    END AS source_url,
    CASE
      -- Hacky for now. But, no way to tell a Zendesk URL vs. a generic URL
      WHEN d.id = highspot.document_id THEN 'highspot'
      WHEN d.id = zendesk.document_id THEN 'zendesk'
      ELSE ''
    END AS external_doc_source,
    j.error
  FROM ${schema}.${constants.TABLE_DOC} d
  -- Temporary until Highspot integration update runs, which will write directly to metadata_json field
  LEFT JOIN ${schema}.${constants.TABLE_HIGHSPOT_ASSET} highspot
    ON d.id = highspot.document_id
  -- Unfortunately, Zendesk doesn't write to source_url or metadata_json fields, so we have to join to the asset table
  LEFT JOIN ${schema}.${constants.TABLE_ZENDESK_ASSET} zendesk
    ON d.id = zendesk.document_id    
  JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} as users
    ON d.created_by_id = users.id
  LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} as dt
    ON d.document_type_id = dt.id
  LEFT JOIN ${schema}.${constants.TABLE_DOC} as d2
    ON d2.prior_version_document_id = d.id
    AND d2.status in ('${constants.DOC_STATUS_PROCESSING}', '${constants.DOC_STATUS_ACTIVE}')
  LEFT JOIN ${schema}.${constants.TABLE_JOB} as j
    ON j.id = d.job_id
  LEFT JOIN (
    SELECT document_id, COUNT(*) as embedding_count
    FROM ${schema}.${constants.TABLE_EMBEDDING}
    GROUP BY document_id
  ) as e
    ON e.document_id = d.id
  `;

  if (hasFilter) {
    queryString += `
    WHERE ${filter}`;
  }

  if (!includeDeleted) {
    queryString += ` ${hasFilter ? 'AND' : 'WHERE'} d.status != '${constants.DOC_STATUS_DELETED}' `;
  }

  if (omitWeb) {
    queryString += ` ${hasFilter || !includeDeleted ? 'AND' : 'WHERE'} d.document_type_id NOT IN (SELECT id FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE} WHERE name='Website') `;
  } else if (webOnly) {
    queryString += ` ${hasFilter || !includeDeleted ? 'AND' : 'WHERE'} d.document_type_id IN (SELECT id FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE} WHERE name='Website') `;
  }

  queryString += `
  ORDER BY d.id DESC`;

  const res = await query(queryString, queryParams ?? []);
  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

//Include review status
export async function getEmbeddingsByDocId(
  schema: string,
  docIds: Array<number>,
  isSlack: boolean = false,
  limit: number = 2000,
  offset: number = 0,
): Promise<Embedding[]> {
  //Slack content_meta is BIG so we so let's use a seperate sub-query to
  //get just what we need.

  //First a sub-sub-query for the user info:
  const slackUserSubQuery = `(SELECT jsonb_agg(
    jsonb_build_object(
          'email', users->>'email',
          'name', users->>'name',
          'is_bot', users->>'is_bot',
          'image_32', users->>'image_32'
        )
    ) as users
    FROM jsonb_array_elements(e.content_meta->'users') AS users)
`;

  const content_meta = isSlack
    ? `jsonb_build_object(
        'channel', e.content_meta->>'channel',
        'is_thread', e.content_meta->>'is_thread',
        'slack_link', e.content_meta->>'slack_link',
        'date', e.content_meta->>'date',
        'users', ${slackUserSubQuery}
      ) as content_meta,
      `
    : `e.content_meta,
`;

  const orderBy = `ORDER BY id ${isSlack ? 'DESC' : 'ASC'}`;
  const limitOffset = `LIMIT ${limit} OFFSET ${offset}`;

  const queryString = `
    SELECT
      e.id,
      e.document_id,
      e.index,
      e.content,
      ${content_meta}
      e.content_type,
      e.use_for_generation,
      e.metadata_filter,
      e.modified_by_id,
      u.name as modified_by_name,
      e.orig_content,
      cr_status.status as review_status,
      cr_status.created_by_name as review_created_by_name,
      cr_status.created_date as review_created_date,
      cr_status.description as review_message,
      cr.id as content_review_id
      FROM ${schema}.${constants.TABLE_EMBEDDING} e
      LEFT JOIN LATERAL (
        SELECT max(id) as id
        FROM ${schema}.${constants.TABLE_CONTENT_REVIEW} cr
        WHERE cr.embedding_id = e.id
        GROUP BY embedding_id
      ) cr ON TRUE
      LEFT JOIN LATERAL (
        SELECT cr2.status, cr2.created_date, cr2.description, u.name as created_by_name
        FROM ${schema}.${constants.TABLE_CONTENT_REVIEW} cr2
        JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} u ON u.id = cr2.created_by_id
        WHERE cr2.id = cr.id
      ) cr_status ON TRUE
      LEFT JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} u ON u.id = e.modified_by_id
    WHERE document_id = ANY($1::int[])
    AND origin = '${process.env.LLM_SYSTEM}'
    ${orderBy}
    ${limitOffset}
    `;
  const res = await query(queryString, [docIds]);
  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

export async function getDocsByIds(
  schema: string,
  docIds: Array<number>,
): Promise<Partial<Document>[]> {
  const table = constants.TABLE_DOC;
  const fields = [
    'id',
    'uuid',
    'file_name',
    'document_revision',
    'cloud_version_id',
    'prior_version_document_id',
    'created_date',
    'created_by_id',
    'status',
    'document_type_id',
    'privacy',
    'use_for_generation',
    'label',
    'description',
    'is_custom_table',
    'metadata_json',
  ];
  const queryString = `
    SELECT ${fields.join(',')}
    FROM ${schema}.${table}
    WHERE id = ANY($1::int[])`;
  const res = await query(queryString, [docIds]);
  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

export async function getDocsByDocTypeId(
  schema: string,
  docTypeId: number,
): Promise<Partial<Document>[]> {
  const fields = [
    'id',
    'uuid',
    'file_name',
    'document_revision',
    'cloud_version_id',
    'prior_version_document_id',
    'created_date',
    'created_by_id',
    'status',
    'document_type_id',
    'privacy',
    'use_for_generation',
    'label',
    'description',
    'is_custom_table',
    'metadata_json',
  ];
  const queryString = `
    SELECT ${fields.join(',')}
    FROM ${schema}.${constants.TABLE_DOC}
    WHERE deleted_date IS NULL AND document_type_id = $1`;
  const res = await query(queryString, [docTypeId]);
  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

// Returns a list records where each record is a list of ids
// that share the same "remember_tags" configuration. Each
// distinct "remember_tags" config is a separate row.
export async function getDocRememberTags(
  schema: string,
  docIds: number[],
): Promise<Partial<Document>[]> {
  const queryString = `
    SELECT 
      tag_value,
      array_agg(id) as id_list
    FROM (
      SELECT 
        id,
        COALESCE(metadata_json->'remember_tags', '{}'::jsonb)#>>'{}' as tag_value
      FROM ${schema}.${constants.TABLE_DOC}
      WHERE id = ANY($1::int[])
    ) subquery
    GROUP BY tag_value  
  `;
  const res = await query(queryString, [docIds]);
  if (res && res.rows) {
    return res.rows;
  }
  return [];
}

export async function getDoc(schema, docId): Promise<Partial<Document>> {
  const table = constants.TABLE_DOC;
  const fields = [
    'd.id',
    'd.uuid',
    'd.job_id',
    'd.file_name',
    'd.label',
    'd.document_revision',
    'd.cloud_version_id',
    'd.prior_version_document_id',
    'd.created_date',
    'd.created_by_id',
    `COALESCE(u.name, '') as created_by_name`,
    'd.status',
    'd.document_type_id',
    'dt.is_rfp',
    'dt.name as type',
    'd.privacy',
    'd.use_for_generation',
    'd2.id as updated_document_id',
    'd2.status as updated_document_status',
    'd.expiry_date',
    'd.is_custom_table',
    'd.source_url',
    'd.metadata_json',
    "d.metadata_json->>'externalDocSource' as external_doc_source",
    "d.metadata_json->>'externalDocSourceId' as external_doc_source_id",
    "d.metadata_json->>'externalDocSourceUrl' as external_doc_source_url",
  ];

  const queryString = `
    SELECT ${fields.join(',')}
    FROM ${schema}.${constants.TABLE_DOC} d
    LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} as dt
      ON dt.id = d.document_type_id
    LEFT JOIN tribble.${constants.TABLE_USER} as u
      ON d.created_by_id = u.id
    LEFT JOIN ${schema}.${constants.TABLE_DOC} as d2
      ON d2.prior_version_document_id = d.id
      AND d2.status in ('${constants.DOC_STATUS_PROCESSING}', '${constants.DOC_STATUS_ACTIVE}')
    WHERE d.id = $1
  `;
  const res = await query(queryString, [docId]);
  if (res && res.rows) {
    return res.rows[0];
  }
  return {};
}

export async function getDocIdsByEmbeddingIds(
  schema: string,
  embeddingIds: number[],
) {
  try {
    const queryString = `
    SELECT DISTINCT document_id
    FROM ${schema}.${constants.TABLE_EMBEDDING}
    WHERE id = ANY($1)`;

    const res = await query(queryString, [embeddingIds]);
    if (res && res.rows) {
      return res.rows.map((row) => Number(row.document_id));
    }
    return [];
  } catch (err) {
    console.error(`[db.getDocIdsByEmbeddingIds] ${err}`);
    return [];
  }
}

//Update a value on an embedding
export async function updateEmbedding(
  schema: string,
  embeddingId: number,
  field: string,
  value: any,
) {
  const queryString = `
  UPDATE ${schema}.${constants.TABLE_EMBEDDING}
  SET ${field} = $1
  WHERE id = $2`;

  const res = await query(queryString, [value, embeddingId]);
  if (res && res.rows) {
    return res.rows[0];
  }
  return {};
}

/**
 * Step 2 of the 3 step doc ingest flow
 * @param {FileWithMetadata[]} filesMetadata
 * @param {string} schema
 * @returns {} FileWithMetadata[] with ids included
 */
export async function insertDocs(
  filesMetadata: FileWithMetadata[],
  schema: string,
): Promise<FileWithMetadata[]> {
  let client;

  try {
    client = await getClient();
    //TODO: batch these inserts together.
    await Promise.all(
      filesMetadata.map(async (file, index) => {
        const {
          file_name,
          created_date,
          created_by_id,
          typeId,
          privacy,
          job_id,
          uuid,
          use_for_generation,
          status,
          label,
          prior_version_document_id,
          expiry_date,
          externalDocSource,
          externalDocSourceId,
          source_url,
          typeIsStructured,
        } = file;

        let defaultTypeId = -1;
        if (typeId == -1) {
          // Use "document" as a default type id if typeId = -1
          const queryStringDefaultDocTypeId = `
          SELECT id
          FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE}
          WHERE name = 'Document'
          OR is_rfp = false
          ORDER BY id
          LIMIT 1
        `;

          let resDefault = await client.query(queryStringDefaultDocTypeId, []);
          if (resDefault && resDefault.rows) {
            defaultTypeId = resDefault.rows[0].id;
          } else {
            // This shouldn't happen. If so, we can't proceed anyway
          }
        }

        const metadata_json = externalDocSource
          ? { externalDocSource, externalDocSourceId }
          : {};

        const queryString = `
      INSERT into ${schema}.${constants.TABLE_DOC}
      (file_name, created_date, created_by_id, document_type_id, privacy, job_id, uuid, use_for_generation, status, label, prior_version_document_id, expiry_date, metadata_json, source_url, is_custom_table)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      RETURNING id;
      `;
        try {
          let res = await client.query(queryString, [
            file_name,
            created_date,
            created_by_id,
            typeId == -1 ? defaultTypeId : typeId,
            privacy,
            job_id,
            uuid,
            use_for_generation,
            status,
            label,
            prior_version_document_id,
            expiry_date,
            metadata_json,
            source_url,
            typeIsStructured,
          ]);
          filesMetadata[index].id = res.rows[0].id;
        } catch (err) {
          console.error(
            `[insertDocs] Error inserting doc [${file_name}]: ${err}`,
          );
          throw err;
        }
      }),
    );
  } catch (err) {
    // Rethrow -- this outer try/catch just to ensure we release the client
    throw err;
  } finally {
    if (client) {
      client.release();
    }
  }

  return filesMetadata;
}

export async function deleteEmbeddingsById(
  schema: string,
  embeddingIds: number[],
) {
  try {
    const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_EMBEDDING} e1
    WHERE EXISTS
    (
      SELECT 1
      FROM ${schema}.${constants.TABLE_EMBEDDING} e2
      WHERE e2.id = ANY($1::int[])
      AND e2.document_id = e1.document_id
      AND e2.index = e1.index
    )`;

    await query(queryString, [embeddingIds]);
  } catch (err) {
    console.error(`[db.deleteEmbeddingsById] ${err}`);
    throw new Error('Failed to delete embeddings');
  }
}

/*
 * Transactional deletion docs and embeddings.
 * @param schema
 * @param docIds
 */
export async function deleteDocsAndEmbeddings(
  schema: string,
  docIds: number[],
  userId: number,
  jobId: number = -1,
) {
  const client = await getClient();
  //   const embeddings = await getEmbeddingsByDocId(schema, docIds);
  //   const embeddingIds = embeddings.map((embedding) => embedding.id);

  try {
    await client.query('BEGIN');

    const queryDeleteEmbeds = `DELETE FROM ${schema}.${constants.TABLE_EMBEDDING} WHERE document_id = ANY($1::int[])`;

    const queryUpdateDocs =
      `UPDATE ${schema}.${constants.TABLE_DOC} ` +
      `SET status = '${constants.DOC_STATUS_DELETED}', deleted_date = $1, deleted_by_id = $2 ` +
      `WHERE id = ANY($3::int[])`;

    await client.query(queryDeleteEmbeds, [docIds]);
    await client.query(queryUpdateDocs, [new Date(), userId, docIds]);

    if (jobId != -1) {
      // Can't delete content (need to keep statistics), rather set is_deleted flag
      const queryUpdateContent =
        `UPDATE ${schema}.${constants.TABLE_CONTENT} ` +
        `SET is_deleted = true ` +
        `WHERE job_id = $1`;
      await client.query(queryUpdateContent, [jobId]);
    }

    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[deleteDocsAndEmbeddings] Unable to delete docs: ${err}`);
    throw err;
  } finally {
    client.release();
  }
}

/*
 * Transactional: soft deletion of old doc id and embeddings, and setting up new.
 * @param schema
 * @param docIds
 */
export async function versionUpdate(
  schema: string,
  oldDoc: Partial<Document>,
  newDoc: Partial<Document>,
) {
  const oldDocId = oldDoc.id;
  const userId = oldDoc.created_by_id;
  const client = await getClient();

  try {
    await client.query('BEGIN');

    // Note: this code is more or less the same as in positronic-ingest-integration/src/functions/db.ts::versionUdate()
    // If updating this, update it there too.

    // Set embedding use_for_generation to false for old doc
    const queryUpdateOldEmbeddings = `
      UPDATE ${schema}.${constants.TABLE_EMBEDDING}
      SET use_for_generation = false
      WHERE document_id = $1
    `;

    //Soft delete doc
    const queryUpdateDocs =
      `UPDATE ${schema}.${constants.TABLE_DOC} ` +
      `SET status = '${constants.DOC_STATUS_DELETED}', use_for_generation = false, deleted_date = $1, deleted_by_id = $2 ` +
      `WHERE id = $3`;

    // Need to apply any of the "remember_tags" from the old doc's metadata_json
    // and apply to the new doc's embeddings. Also, write the "remember_tags" into
    // the new doc's metadata_json
    const oldDocHasRememberTags = oldDoc.metadata_json?.remember_tags;

    // Also, for the new doc -- inherit the privacy setting of the old doc
    const querySetUseForGen = oldDocHasRememberTags
      ? `
      UPDATE ${schema}.${constants.TABLE_DOC}
      SET use_for_generation = true, privacy = $1, metadata_json['remember_tags'] = $2::jsonb
      WHERE id = $3
    `
      : `
      UPDATE ${schema}.${constants.TABLE_DOC}
      SET use_for_generation = true, privacy = $1
      WHERE id = $2
    `;

    console.log('[versionUpdate] Soft deleting old embeddings and doc');
    await client.query(queryUpdateOldEmbeddings, [oldDocId]);
    await client.query(queryUpdateDocs, [new Date(), userId, oldDocId]);

    console.log('[versionUpdate] Setting old doc settings to new doc');
    await client.query(
      querySetUseForGen,
      oldDocHasRememberTags
        ? [oldDoc.privacy ?? 'public', oldDocHasRememberTags, newDoc.id]
        : [oldDoc.privacy ?? 'public', newDoc.id],
    );

    // Need to apply the tags to the newly created embeddings
    if (oldDocHasRememberTags && oldDocHasRememberTags.add_tags) {
      console.log('[versionUpdate] Setting old doc has remember_tags');

      const queryNewDocEmbeddings = `
        SELECT id, metadata_filter
        FROM ${schema}.${constants.TABLE_EMBEDDING}
        WHERE document_id = $1
      `;

      const newDocEmbeddingMdf = await client.query(queryNewDocEmbeddings, [
        newDoc.id,
      ]);
      if (newDocEmbeddingMdf && newDocEmbeddingMdf.rows) {
        console.log(
          `[versionUpdate] Setting remember_tags to ${newDocEmbeddingMdf.rows.length} embeddings`,
        );

        const updatedDocEmbeddingMdf = newDocEmbeddingMdf.rows.map((row) => {
          // Need to generate the expected shape of the metadata_filter field
          const newMdf = oldDoc.metadata_json.remember_tags.add_tags.reduce(
            (acc, tag) => {
              if (!acc[String(tag.type_id)]) {
                acc[String(tag.type_id)] = [];
              }

              if (!acc[String(tag.type_id)].includes(tag.value)) {
                acc[String(tag.type_id)].push(tag.value);
              }

              return acc;
            },
            // Just in case embedding MDFs already exist, need to ensure we merge nicely
            row.metadata_filter ?? {},
          );

          return {
            id: row.id,
            metadata_filter: newMdf,
          };
        });

        const mdfValues = updatedDocEmbeddingMdf
          .map(
            (item) =>
              `(${item.id}, '${JSON.stringify(item.metadata_filter)}'::jsonb)`,
          )
          .join(',\n\t');

        // Now, bulk update all the embedding records
        const updateNewDocEmbeddingsMdf = `
          UPDATE ${schema}.${constants.TABLE_EMBEDDING} e
          SET metadata_filter = temp.mdf
          FROM (VALUES ${mdfValues}) AS temp(id, mdf)
          WHERE e.id = temp.id        
        `;

        await client.query(updateNewDocEmbeddingsMdf, []);
      }
    }

    //Handle custom tables
    if (oldDoc.is_custom_table) {
      const customTable = await client.query(
        `SELECT table_name, id FROM ${schema}.${constants.TABLE_TABLE_CATALOG} WHERE document_id = $1`,
        [oldDocId],
      );
      if (customTable?.rows?.length > 0) {
        const tableIds = customTable.rows.map((row) => row.id);
        const tableName = customTable.rows[0].table_name;
        await client.query(
          `DELETE FROM ${schema}.${constants.TABLE_TABLE_CATALOG} where id = ANY($1::int[])`,
          [tableIds],
        );
        await client.query(
          `DROP TABLE IF EXISTS ${getCustomSchema(schema)}.${tableName}`,
        );
      }
    }

    await client.query('COMMIT');
    console.log('[versionUpdate] Success');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[versionUpdate] Unable to update version: ${err}`);
    throw err;
  } finally {
    client.release();
  }
}

export async function getDocTypes(
  schema: string,
  typeName?: string,
): Promise<DocumentType[]> {
  const queryString = `
  SELECT
    dt.id,
    CASE
      WHEN dt.name = 'Spreadsheet' THEN 'Database'
      WHEN dt.name = 'Questionnaire' THEN 'Q&A Spreadsheet'
      ELSE dt.name
    END as name,
    dt.priority_id,
    dt.is_rfp,
    dt.is_spreadsheet,
    dt.is_system,
    p.name as priority_name
  FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt
  JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE_PRIORITY} p ON dt.priority_id = p.id
  ${typeName ? `WHERE dt.name = '${typeName}'` : ''}
  ORDER BY dt.id DESC
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows;
  } else {
    return [];
  }
}

export async function getPriorities(
  schema: string,
): Promise<DocumentTypePriority[]> {
  const queryString = `
  SELECT
    id,
    name,
    multiplier
  FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE_PRIORITY}
  ORDER BY id ASC
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows;
  } else {
    return [];
  }
}

export async function insertNewDocType(schema: string, docType: DocumentType) {
  try {
    const queryString = `
    INSERT into ${schema}.${constants.TABLE_DOCUMENT_TYPE}
    (name, priority_id, is_rfp, is_spreadsheet)
    VALUES ($1, $2, $3, $4)
    RETURNING id;
    `;
    const result = await query(queryString, [
      docType.name,
      docType.priority_id,
      docType.is_rfp,
      docType.is_spreadsheet,
    ]);
    return result.rows[0].id;
  } catch (err) {
    console.error(`[insertNewDocType] ${err}`);
    return '';
  }
}

export async function updateDocType(schema: string, docType: DocumentType) {
  try {
    const id = docType.id;
    const name = docType.name || '';
    const priority_id = docType.priority_id;
    const is_rfp = docType.is_rfp;
    const is_spreadsheet = docType.is_spreadsheet;

    const queryString = `
      UPDATE ${schema}.${constants.TABLE_DOCUMENT_TYPE}
      SET name = $1, priority_id = $2, is_rfp = $3, is_spreadsheet = $4
      WHERE id = $5
      RETURNING id;`;

    const result = await query(queryString, [
      name,
      priority_id,
      is_rfp,
      is_spreadsheet,
      id,
    ]);

    if (result && result.rows) {
      return true;
    } else {
      return false;
    }
  } catch (err) {
    console.error(`[updateDocType] ${err}`);
    return false;
  }
}

export async function deleteDocType(schema: string, docTypeId: number) {
  try {
    const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_DOCUMENT_TYPE}
    WHERE id = $1`;
    const result = await query(queryString, [docTypeId]);
    return result;
  } catch (err) {
    console.error(`[deleteDocType] ${err.message}`);
    return false;
  }
}

export async function getUsersWhoUploadedDocs(schema: string) {
  const queryString = `
  SELECT DISTINCT
    u.id,
    u.name
  FROM ${schema}.${constants.TABLE_DOC} d
  JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} u
    ON d.created_by_id = u.id
  ORDER BY u.name
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows;
  } else {
    return [];
  }
}

export async function getActiveDocCountByType(
  schema: string,
  maxDate: string,
  minDate: string,
  sourceTypes: number[],
  statuses: string[],
  uploadedBy: number[],
  searchValue: string,
) {
  const params: any[] = [];
  const conditions: string[] = [];
  let paramIndex = 1;

  let queryString = `
  SELECT
    dt.name,
    COUNT(d.id) as count,
  CASE
    WHEN d.metadata_json->>'externalDocSource' IS NOT NULL AND d.metadata_json->>'externalDocSource' != '' THEN d.metadata_json->>'externalDocSource'
    ELSE NULL
  END as other
  FROM ${schema}.${constants.TABLE_DOC} d
  JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt
    ON d.document_type_id = dt.id
  WHERE d.status != 'deleted'
    AND d.use_for_generation = true
    AND NOT EXISTS (
      SELECT 1
      FROM ${schema}.${constants.TABLE_DOC} newer_d
      WHERE newer_d.prior_version_document_id = d.id
        AND newer_d.status != 'deleted'
        AND newer_d.use_for_generation = true
    )`;

  if (maxDate) {
    conditions.push(`d.created_date <= $${paramIndex}`);
    params.push(maxDate);
    paramIndex++;
  }
  if (minDate) {
    conditions.push(`d.created_date >= $${paramIndex}`);
    params.push(minDate);
    paramIndex++;
  }
  if (sourceTypes && sourceTypes.length > 0) {
    conditions.push(`dt.id = ANY($${paramIndex})`);
    params.push(sourceTypes);
    paramIndex++;
  }
  if (statuses && statuses.length > 0) {
    conditions.push(`d.status = ANY($${paramIndex})`);
    params.push(statuses);
    paramIndex++;
  }
  if (uploadedBy && uploadedBy.length > 0) {
    conditions.push(`d.created_by_id = ANY($${paramIndex})`);
    params.push(uploadedBy);
    paramIndex++;
  }
  if (searchValue) {
    conditions.push(`d.file_name ILIKE $${paramIndex}`);
    params.push(`%${searchValue}%`);
    paramIndex++;
  }

  if (conditions.length > 0) {
    queryString += ` AND ${conditions.join(' AND ')}`;
  }

  queryString += `
  GROUP BY dt.name,
  CASE
    WHEN d.metadata_json->>'externalDocSource' IS NOT NULL AND d.metadata_json->>'externalDocSource' != '' THEN d.metadata_json->>'externalDocSource'
    ELSE NULL
  END
  ORDER BY dt.name
  `;

  const result = await query(queryString, params);
  if (result && result.rows) {
    return result.rows;
  } else {
    return [];
  }
}

export async function getDocDateRange(schema: string) {
  const queryString = `
  SELECT
    MIN(created_date) as min,
    MAX(created_date) as max
  FROM ${schema}.${constants.TABLE_DOC}
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows[0];
  } else {
    return {};
  }
}

export async function addMetadataFilterType(
  schema: string,
  mdfType: MetadataFilterType,
) {
  try {
    const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_METADATA_FILTER_TYPE} (name, distinct_answer, is_verbatim)
      VALUES ($1, $2, $3)
      RETURNING id`;

    const result = await query(queryString, [
      mdfType.name,
      mdfType.distinct_answer,
      mdfType.is_verbatim,
    ]);
    return result.rows[0].id;
  } catch (err) {
    console.error(`[addMetadataFilterType] ${err}`);
    return -1;
  }
}

// Return a "status" code: 1 = success, 0 = in use, -1 = error
export async function deleteMetadataFilterType(
  schema: string,
  mdfTypeId: Number,
) {
  try {
    // Need to check that the MDF type is not used in any embedding or any content metadata filter
    const countQueryString = `
      SELECT c.cnt + e.cnt count
      FROM
        ( SELECT COUNT(*) cnt
          FROM ${schema}.${constants.TABLE_CONTENT}
          WHERE (metadata_filter -> 'type_id')::int = $1
          AND is_deleted != true
        ) as c,
        ( SELECT COUNT(*) cnt
          FROM ${schema}.${constants.TABLE_EMBEDDING}
          WHERE metadata_filter -> $2 is not null
        ) as e`;

    const result = await query(countQueryString, [
      mdfTypeId,
      String(mdfTypeId),
    ]);
    const recordCount = result.rows[0].count;

    if (recordCount == 0) {
      const deleteQueryString1 = `DELETE FROM ${schema}.${constants.TABLE_METADATA_FILTERS} WHERE type_id = $1`;
      const deleteQueryString2 = `DELETE FROM ${schema}.${constants.TABLE_METADATA_FILTER_TYPE} WHERE id = $1`;

      await query(deleteQueryString1, [mdfTypeId]);
      await query(deleteQueryString2, [mdfTypeId]);

      return 1;
    } else {
      console.error(
        `[deleteMetadataFilterType] Could not delete Metadata Filter Type: it is referenced in existing records`,
      );
      return 0;
    }
  } catch (err) {
    console.error(`[deleteMetadataFilterType] ${err}`);
    return -1;
  }
}

export async function updateMetadataFilterType(
  schema: string,
  mdfType: MetadataFilterType,
) {
  try {
    const { id, name, distinct_answer, is_verbatim } = mdfType;

    const queryString = `
      UPDATE ${schema}.${constants.TABLE_METADATA_FILTER_TYPE}
      SET name = $1, distinct_answer = $2, is_verbatim = $3
      WHERE id = $4`;

    const result = await query(queryString, [
      name,
      distinct_answer,
      is_verbatim,
      id,
    ]);
    return true;
  } catch (err) {
    console.error(`[updateMetadataFilterType] ${err}`);
    return false;
  }
}

export async function addMetadataFilterValues(
  schema: string,
  newValues: NewMetadataFilterValue[],
) {
  try {
    // Not most efficient, but, guessing we won't be updating a lot of values at once
    const insertPromises = newValues.map(async (value) => {
      const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_METADATA_FILTERS} (type_id, value, is_active, synonyms, description)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id`;

      const result = await query(queryString, [
        value.type_id,
        value.value,
        true,
        value.synonyms,
        value.description,
      ]);

      return result.rows[0];
    });

    const insertedRecords = await Promise.all(insertPromises);
    return insertedRecords;
  } catch (err) {
    console.error(`[addMetadataFilterValues] ${err}`);
    return [];
  }
}

export async function updateMetadataFilterValues(
  schema: string,
  updateValues: MetadataFilterValue[],
) {
  try {
    // Not most efficient, but, guessing we won't be updating a lot of values at once
    updateValues.forEach(async (value) => {
      const queryString = `
      UPDATE ${schema}.${constants.TABLE_METADATA_FILTERS}
      SET is_active = $1,
          description = $2,
          synonyms = $3,
          rfp_default = $4
      WHERE id = $5`;

      const result = await query(queryString, [
        value.is_active,
        value.description,
        value.synonyms,
        value.rfp_default ?? false,
        value.id,
      ]);
    });

    return true;
  } catch (err) {
    console.error(`[updateMetadataFilterValues] ${err}`);
    return false;
  }
}

/**
 * Get metadata filter records. More structured than getMetadataFilters.
 */
export async function getMetadataFilterRecords(
  schema: string,
  metadata_filter_id: number = null,
): Promise<MetadataFilterValue[]> {
  let metadata_filter_id_clause = '';
  let query_params = [];
  if (metadata_filter_id != null) {
    metadata_filter_id_clause = `WHERE mdf.id = $1`;
    query_params = [metadata_filter_id];
  }

  const queryString = `
  SELECT
    mdf.id,
    mdf.type_id,
    mdf.value,
    mdf.is_active,
    mdf_type.name as type_name,
    mdf_type.distinct_answer as distinct_answer,
    mdf.synonyms,
    mdf.description,
    mdf.rfp_default
  FROM ${schema}.${constants.TABLE_METADATA_FILTERS} mdf
  JOIN ${schema}.${constants.TABLE_METADATA_FILTER_TYPE} mdf_type ON mdf.type_id = mdf_type.id
  ${metadata_filter_id_clause}
  `;

  const result = await query(queryString, query_params);
  if (result && result.rows && result.rows.length) {
    return result.rows.map((mdf) => {
      return {
        id: mdf.id,
        type_id: mdf.type_id,
        value: mdf.value,
        is_active: mdf.is_active,
        type: {
          id: mdf.type_id,
          name: mdf.type_name,
        },
        synonyms: mdf.synonyms,
        description: mdf.description,
        rfp_default: mdf.rfp_default ?? false,
      };
    });
  } else {
    return [];
  }
}

export async function updateEmbeddingRecord(
  schema: string,
  user_id: number,
  embedding_id: number,
  new_content: string,
  new_embedding: number[],
  new_metadata_filter: any,
  isModified: boolean,
  origin: string,
) {
  try {
    // Don't filter by origin -- update them all.
    const queryString = isModified
      ? `UPDATE ${schema}.${constants.TABLE_EMBEDDING} AS current
                        SET content = $1
                        ,   embedding = '${JSON.stringify(new_embedding)}'::vector
                        ,   metadata_filter = $2
                        ,   modified_by_id = $3
                        ,   modified_date = $4
                        WHERE id IN (
                          SELECT  e2.id
                          FROM    ${schema}.${constants.TABLE_EMBEDDING} e1
                          JOIN    ${schema}.${constants.TABLE_EMBEDDING} e2
                                  ON  e1.document_id = e2.document_id
                                  AND e1.index = e2.index
                                  AND e2.origin = $5
                          WHERE   e1.id = $6
                        ) RETURNING id`
      : `UPDATE ${schema}.${constants.TABLE_EMBEDDING} AS current
                        SET content = $1
                        ,   embedding = '${JSON.stringify(new_embedding)}'::vector
                        ,   metadata_filter = $2
                        ,   orig_content = (SELECT  e.content as orig_content
                                            FROM    ${schema}.${constants.TABLE_EMBEDDING} e
                                            WHERE   e.id = current.id)
                        ,   orig_metadata_filter = (SELECT  e.metadata_filter as orig_metadata_filter
                                                    FROM    ${schema}.${constants.TABLE_EMBEDDING} e
                                                    WHERE   e.id = current.id)
                        ,   orig_embedding = (SELECT  e.embedding as orig_embedding
                                              FROM    ${schema}.${constants.TABLE_EMBEDDING} e
                                              WHERE   e.id = current.id)
                        ,   modified_by_id = $3
                        ,   modified_date = $4
                        WHERE id IN (
                          SELECT  e2.id
                          FROM    ${schema}.${constants.TABLE_EMBEDDING} e1
                          JOIN    ${schema}.${constants.TABLE_EMBEDDING} e2
                                  ON  e1.document_id = e2.document_id
                                  AND e1.index = e2.index
                                  AND e2.origin = $5
                          WHERE   e1.id = $6
                        ) RETURNING id`;

    const result = await query(queryString, [
      new_content,
      new_metadata_filter,
      user_id,
      new Date(),
      origin,
      embedding_id,
    ]);

    await createEmbeddingHistoryRecord(
      schema,
      user_id,
      embedding_id,
      new_content,
      new_metadata_filter,
    );

    return true;
  } catch (err) {
    console.error(
      `[updateEmbeddingRecord] Error updating record id=[${embedding_id}], origin=[${origin}]`,
      err,
    );
    return false;
  }
}

export async function revertEmbeddingRecord(
  schema: string,
  embedding_id: number,
  user_id: number,
) {
  try {
    // Don't filter by origin -- update them all.
    const queryString = `UPDATE ${schema}.${constants.TABLE_EMBEDDING} AS current
                        SET content = orig_content
                        ,   embedding = orig_embedding
                        ,   metadata_filter = orig_metadata_filter
                        ,   modified_by_id = NULL
                        ,   modified_date = NULL
                        ,   orig_content = NULL
                        ,   orig_embedding = NULL
                        ,   orig_metadata_filter = '{}'::jsonb
                        WHERE id IN (
                          SELECT  e2.id
                          FROM    ${schema}.${constants.TABLE_EMBEDDING} e1
                          JOIN    ${schema}.${constants.TABLE_EMBEDDING} e2
                                  ON  e1.document_id = e2.document_id
                                  AND e1.index = e2.index
                          WHERE   e1.id = $1
                        ) RETURNING *`;

    const result = await query(queryString, [embedding_id]);

    if (result.rows?.length > 0) {
      await createEmbeddingHistoryRecord(
        schema,
        user_id,
        embedding_id,
        result.rows[0].content,
        result.rows[0].metadata_filter,
      );
      return true;
    } else {
      return false;
    }
  } catch (err) {
    console.error(`[updateDocType] ${err}`);
    return false;
  }
}

async function createEmbeddingHistoryRecord(
  schema: string,
  user_id: number,
  embedding_id: number,
  content: string,
  metadata_filter: any,
) {
  try {
    const queryString = `
                        INSERT INTO ${schema}.${constants.TABLE_EMBEDDING_HISTORY}
                          (embedding_id, user_id, history_date, history_content, history_metadata_filter)
                        VALUES
                          ($1, $2, $3, $4, $5);`;

    await query(queryString, [
      embedding_id,
      user_id,
      new Date(),
      content,
      metadata_filter,
    ]);
    return true;
  } catch (err) {
    console.error(
      `[createEmbeddingHistoryRecord] Error inserting record embedding_id=[${embedding_id}]`,
      err,
    );
    return false;
  }
}

export async function findEmbeddings(
  schema: string,
  embedding: number[],
  origin: string,
  distance_threshold: number,
  max_results: number,
) {
  try {
    // Don't filter by origin -- update them all.
    const queryString = `
      SELECT  json_build_object(
                      'similarity_score', 1 - (e.embedding <=> '${JSON.stringify(embedding)}'::vector),
                      'text', content,
                      'metadata', content_meta,
                      'document_id', document_id,
                      'file_name', file_name,
                      'uuid', uuid,
                      'embedding_id', e.id,
                      'metadata_filter', e.metadata_filter
              ) as result
      FROM    ${schema}.embedding e
      ,             ${schema}.document d
      WHERE   e.document_id = d.id
      AND     e.origin = '${origin}'
      AND         e.use_for_generation = true
      AND     (e.embedding <=> '${JSON.stringify(embedding)}'::vector) < ${distance_threshold}
      ORDER BY
              e.embedding <=> '${JSON.stringify(embedding)}'::vector
      LIMIT   ${max_results}`;

    const result = await query(queryString, []);
    if (result.rows) {
      return result.rows;
    } else {
      return [];
    }
  } catch (err) {
    console.error(
      `[findEmbeddings] Error finding embeddings, origin=[${origin}]`,
      err,
    );
    return [];
  }
}

export async function getEmbeddingJustTags(schema: string, docId: number) {
  try {
    // It's messy, but there could be existing tags on any given text snippet
    // in the document. So we need to grab the records
    // (can restrict to id and tag fields to save memory), do the updates in Javascript land
    // (because we're dealing with very nested structures), and then bulk update the records.
    const queryGetEmbeddings = `
      SELECT id, metadata_filter, orig_metadata_filter, modified_date
      FROM ${schema}.${constants.TABLE_EMBEDDING}
      WHERE document_id = $1
    `;

    const result = await query(queryGetEmbeddings, [docId]);
    if (result.rows) {
      return result.rows;
    } else {
      return [];
    }
  } catch (err) {
    console.error(`[getEmbeddingJustTags] Error getting embeddings`, err);
    throw err;
  }
}

export async function getEmbeddingCountByDocIds(
  schema: string,
  docIds: number[],
) {
  const queryString = `
    SELECT document_id, COUNT(*) as count
    FROM ${schema}.${constants.TABLE_EMBEDDING}
    WHERE document_id = ANY($1::int[])
    GROUP BY document_id
  `;

  const result = await query(queryString, [docIds]);
  if (result.rows) {
    return result.rows;
  } else {
    return [];
  }
}

export async function updateEmbeddingTags(
  schema: string,
  embeddings: [],
  user_id: number,
) {
  const client = await getClient();

  try {
    await client.query('BEGIN');

    const queryUpdateEmbeddings =
      `UPDATE ${schema}.${constants.TABLE_EMBEDDING} ` +
      `SET metadata_filter = $1, orig_metadata_filter = $2 ` +
      `WHERE id = $3`;

    // Write embedding_history record.
    // Since only MDF changing, copy the content from the embedding table.
    const queryInsertEmbeddingHistory =
      `INSERT INTO ${schema}.${constants.TABLE_EMBEDDING_HISTORY} ` +
      `  (embedding_id, user_id, history_date, history_content, history_metadata_filter)` +
      `VALUES ($1, $2, $3, (SELECT content FROM ${schema}.${constants.TABLE_EMBEDDING} WHERE id = $1), $4)`;

    const promises = [];
    embeddings.forEach((embedding: any) => {
      promises.push(
        client.query(queryUpdateEmbeddings, [
          embedding.metadata_filter,
          embedding.orig_metadata_filter,
          embedding.id,
        ]),
        client.query(queryInsertEmbeddingHistory, [
          embedding.id,
          user_id,
          new Date(),
          embedding.metadata_filter,
        ]),
      );
    });

    await Promise.all(promises);
    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[updateEmbeddingTags] Error saving embeddings tags: ${err}`);
    throw err;
  } finally {
    client.release();
  }
}

export const getEmbeddingByDocAndIndex = async (
  docId: number,
  index: number,
  schema: string,
) => {
  const queryString = `
  SELECT 
    id, 
    document_id, 
    index, 
    content, 
    content_meta, 
    content_type, 
    use_for_generation, 
    metadata_filter, 
    origin, 
    embedding, 
    modified_by_id
  FROM ${schema}.${constants.TABLE_EMBEDDING}
  WHERE 
    document_id = $1 
  AND
    index = $2
  `;

  try {
    const res = await query(queryString, [docId, index]);
    if (res && res.rows && res.rows.length) {
      return res.rows[0];
    }
    return null;
  } catch (err) {
    console.error(`[getEmbeddingByDocAndIndex] ${err}`);
    return null;
  }
};

/**
 * Check if an embedding with this document_id and index exists. If so, update it.
 * Otherwise, insert a new embedding.
 * A less hacky way to do this would be to use the ON CONFLICT, but
 * index is not a unique key, and making it one would be a pain
 * in the butt. #TODO.
 */
export const upsertEmbedding = async (
  embedding: Embedding,
  schema: string,
  userId: number,
) => {
  try {
    const existingEmbedding = await getEmbeddingByDocAndIndex(
      embedding.document_id,
      embedding.index,
      schema,
    );

    let queryString = `
    INSERT INTO ${schema}.${constants.TABLE_EMBEDDING}
    ( document_id, 
      index, 
      content, 
      content_meta, 
      content_type, 
      use_for_generation, 
      metadata_filter, 
      origin, 
      embedding, 
      modified_by_id,
      modified_date
      )
    VALUES ($1, 
      $2, 
      $3, 
      $4, 
      $5, 
      $6, 
      $7, 
      $8, 
      $9::vector, 
      $10,
      CURRENT_DATE)
      RETURNING id
    `;

    if (existingEmbedding) {
      //update instead
      queryString = `
      UPDATE ${schema}.${constants.TABLE_EMBEDDING}
      SET
          document_id = $1,
          index = $2,
          content = $3,
          content_meta = $4,
          content_type = $5,
          use_for_generation = $6,
          metadata_filter = $7,
          origin = $8,
          embedding = $9::vector,
          modified_by_id = $10,
          modified_date = CURRENT_DATE
      where id = ${existingEmbedding.id}
      RETURNING id`;
    }

    const result = await query(queryString, [
      embedding.document_id,
      embedding.index,
      embedding.content,
      embedding.content_meta,
      embedding.content_type,
      embedding.use_for_generation,
      embedding.metadata_filter,
      embedding.origin,
      JSON.stringify(embedding.embedding),
      embedding.modified_by_id,
    ]);

    if (result && result.rows && result.rows.length) {
      await createEmbeddingHistoryRecord(
        schema,
        userId,
        result.rows[0].id,
        embedding.content,
        embedding.metadata_filter,
      );

      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[upsertEmbedding] ${err}`);
  }
};

//Get the most recent document with the given filename
export const getDocByFileName = async (
  schema: string,
  filename: string,
): Promise<Partial<Document> & { num_embedding: number }> => {
  const queryString = `
  SELECT 
    d.id,
    d.use_for_generation,
    d.status,
    d.expiry_date,
    d.metadata_json,
    d.privacy,
    COALESCE(e.num_embedding, 0) as num_embedding
  FROM ${schema}.${constants.TABLE_DOC} d
  LEFT JOIN (
    SELECT document_id, max(index) as num_embedding
    FROM ${schema}.${constants.TABLE_EMBEDDING}
    GROUP BY document_id
  ) e ON e.document_id = d.id
  WHERE d.file_name = $1
  AND d.deleted_date is null
  ORDER BY d.id DESC
  LIMIT 1;
  `;
  try {
    const res = await query(queryString, [filename]);
    if (res && res.rows && res.rows.length) {
      return res.rows[0];
    }
    return null;
  } catch (err) {
    console.error(`[getDocByFileName] ${err}`);
  }
};

export async function getCustomTableByDocId(schema: string, docId: number) {
  const db = await getDB(schema);
  try {
    return await db
      .selectFrom('table_catalog')
      .select(['document_id', 'table_name', 'id'])
      .where('document_id', '=', docId as any)
      .executeTakeFirst();
  } catch (err) {
    console.error(`[getCustomTableByDocId] ${err}`);
    return null;
  }
}

export async function getCustomTable(schema: string, customTableName: string) {
  const customSchema = `${schema}_custom`;
  const db = await getDB(customSchema);
  try {
    return await db
      .selectFrom(customTableName as any)
      .selectAll()
      .execute();
  } catch (err) {
    console.error(`[getCustomTable] ${err}`);
    return null;
  }
}

export async function deleteCustomTableByDocId(schema: string, docId: number) {
  const db = await getDB();
  const customTable = await getCustomTableByDocId(schema, docId);
  if (!customTable) return;

  try {
    await db
      .withSchema(schema)
      .deleteFrom(`table_catalog`)
      .where('document_id', '=', docId as any)
      .execute();
  } catch (err) {
    console.error(`[deleteCustomTableByDocId] ${err}`);
    return null;
  }
  if (!customTable) return;

  try {
    const queryString = `DROP TABLE IF EXISTS ${getCustomSchema(schema)}.${customTable.table_name}`;
    await query(queryString);
  } catch (err) {
    console.error(
      `[deleteCustomTableByDocId custom table deletion failed] ${err}`,
    );
    return null;
  }
}

export type DocumentWithPreviewUuid = Awaited<
  ReturnType<typeof getDocumentWithPreviewUuid>
>;

export async function getDocumentWithPreviewUuid(
  dbOrTrx: DB | TRX,
  docId: DocumentId,
) {
  const dbQuery = dbOrTrx
    .selectFrom('document as doc')
    .leftJoin('document_type as dt', 'dt.id', 'doc.document_type_id')
    .where('doc.id', '=', docId)
    .select([
      'doc.id',
      'doc.uuid',
      'doc.preview_uuid',
      'doc.file_name',
      'doc.label',
      'doc.status',
      'doc.metadata_json',
      'doc.file_type',
      'dt.name as document_type',
      'dt.is_rfp as is_rfp',
      'dt.is_website as is_website',
    ]);

  return dbQuery.executeTakeFirst();
}

// Used by the download document endpoint.
// Difference vs. the preview one is it checks against integration_asset_sheet.
export async function getDocumentWithUuid(
  dbOrTrx: DB | TRX,
  docId: DocumentId,
) {
  const dbQuery = dbOrTrx
    .selectFrom('document as doc')
    .leftJoin('document_type as dt', 'dt.id', 'doc.document_type_id')
    .leftJoin('integration_asset_sheet as ias', 'ias.document_id', 'doc.id')
    .where('doc.id', '=', docId)
    .select([
      'doc.id',
      'doc.uuid',
      'doc.preview_uuid',
      'doc.file_name',
      'doc.label',
      'doc.status',
      'doc.metadata_json',
      'doc.file_type',
      'dt.name as document_type',
      'dt.is_rfp as is_rfp',
      'dt.is_website as is_website',
      'ias.blob_id as sheet_blob_id',
    ]);

  return dbQuery.executeTakeFirst();
}

export async function saveFiletype(
  dbOrTrx: DB | TRX,
  docId: DocumentId,
  fileType: string,
) {
  return dbOrTrx
    .updateTable('document')
    .set('file_type', fileType)
    .where('id', '=', docId)
    .execute();
}

export async function savePreviewUuid(
  db: DB | TRX,
  docId: DocumentId,
  previewUuid: string,
): Promise<void> {
  await db
    .updateTable('document')
    .set({ preview_uuid: previewUuid })
    .where('id', '=', docId)
    .execute();
}

type ConfluencePageLinks = {
  self: string;
  webui: string;
  tinyui: string;
  edit: string;
};

export async function getConfluencePageLinks(
  schema: string,
  pageId: string,
): Promise<ConfluencePageLinks | null> {
  const intSchema = getIntegrationSchema(schema);
  const queryString = `SELECT
      pages._links
      FROM ${intSchema}.${constants.TABLE_CONFLUENCE_PAGES} pages
      WHERE pages.id = $1
      LIMIT 1`;
  try {
    const result = await queryIntegration(queryString, [pageId]);
    if (result && result.rows && result.rows.length) {
      return result.rows[0]._links;
    }
    return null;
  } catch (err) {
    console.error(`[getConfluencePage] ${err}`);
    return null;
  }
}

export async function updateSourceUrl(
  dbOrTrx: DB | TRX,
  docId: DocumentId,
  sourceUrl: string,
) {
  const dbQuery = dbOrTrx
    .updateTable('document')
    .set('source_url', sourceUrl)
    .where('id', '=', docId);

  return dbQuery.execute();
}

export async function getDocumentByContentDetailId(
  db: DB | TRX,
  contentDetailId: ContentDetailId,
) {
  const dbQuery = db
    .selectFrom('document')
    .innerJoin('job', 'job.id', 'document.job_id')
    .innerJoin('content', 'content.job_id', 'job.id')
    .innerJoin('content_detail', 'content_detail.content_id', 'content.id')
    .where('content_detail.id', '=', contentDetailId)
    .selectAll('document');

  return dbQuery.executeTakeFirst();
}

export async function insertWrapperDoc(
  schema: string,
  doc: Partial<
    Pick<
      Document,
      | 'file_name'
      | 'label'
      | 'status'
      | 'privacy'
      | 'use_for_generation'
      | 'created_by_id'
    >
  >,
  docTypeName?: string,
) {
  try {
    const docType = await getOrCreateDocTypeIdByName(
      schema,
      docTypeName ?? constants.DOC_TYPE_DOC,
    );
    const document = {
      ...doc,
      document_type_id: docType,
    } as Partial<Document>;

    document.uuid = uuidv4();

    const queryString = `
    INSERT INTO ${schema}.${constants.TABLE_DOC} (
      file_name,
      label,
      status,
      privacy,
      use_for_generation,
      document_type_id,
      uuid,
      created_by_id,
      created_date
    ) 
    VALUES (
      $1,
      $2,
      $3,
      $4,
      $5,
      $6,
      $7,
      $8,
      now()
    ) 
    RETURNING id`;

    const values = [
      document.file_name,
      document.label,
      document.status,
      document.privacy,
      document.use_for_generation,
      document.document_type_id,
      document.uuid,
      document.created_by_id,
    ];
    const result = await query(queryString, values);
    return result.rows[0].id;
  } catch (err) {
    console.error(`[insertDocWrapper] Error: ${err}`);
  }
}

export async function upsertDocumentMetadataEmbedding(
  schema: string,
  documentId: number,
  type: string,
  labelEmbedding: number[],
): Promise<void> {
  const queryString = `INSERT INTO ${schema}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING} (
      document_id,
      type,
      label_embedding
    ) VALUES ($1, $2, $3::vector)
    ON CONFLICT (document_id, type) DO UPDATE SET
      label_embedding = $3::vector`;
  try {
    await query(queryString, [
      documentId,
      type,
      JSON.stringify(labelEmbedding),
    ]);
  } catch (err) {
    console.error(`[upsertDocumentMetadataEmbedding] ${err}`);
    throw err;
  }
}

export async function deleteDocumentMetadataEmbedding(
  schema: string,
  documentId: number,
  type: string,
) {
  const queryString = `DELETE FROM ${schema}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING} WHERE document_id = $1 AND type = $2`;
  try {
    await query(queryString, [documentId, type]);
  } catch (err) {
    console.error(`[deleteDocumentMetadataEmbedding] ${err}`);
    throw err;
  }
}

export async function getOrInsertInsightsWrapperDoc(
  schema: string,
  tribbleUserId: number,
): Promise<{ id: number; last_index: number }> {
  try {
    const wrapperDocFileName = constants.DOC_FILE_NAME_INSIGHTS_BASE;
    const doc = await getDocByFileName(schema, wrapperDocFileName);
    if (doc)
      return {
        id: doc.id,
        last_index: doc.num_embedding,
      };

    //Doesn't exist. Insert
    const insightDocType = await getDocTypes(
      schema,
      constants.DOC_TYPE_INSIGHTS,
    );
    if (!insightDocType || insightDocType.length === 0) {
      throw new Error('Insights doc type not found');
    }

    const newDoc = {
      file_name: constants.DOC_FILE_NAME_INSIGHTS_BASE,
      label: constants.DOC_LABEL_INSIGHTS_BASE,
      status: constants.DOC_STATUS_ACTIVE,
      privacy: 'public',
      created_by_id: tribbleUserId,
      use_for_generation: true,
      document_type_id: insightDocType[0].id,
    };

    const wrapperId = await insertWrapperDoc(schema, newDoc, 'Insight');
    return {
      id: wrapperId,
      last_index: -1, //No embeddings yet.
    };
  } catch (err) {
    console.error(`[getOrInsertInsightsWrapperDoc] Error: ${err}`);
  }
}
