// Changes: Added UI display for toolCompletionBlocks to show learn_a_fact interface. Fixed fact content display to properly access block.text.text property.
// Displays the blocks with interactive buttons for Accept, Change, and Dismiss actions
// Users can now accept facts directly or edit them before submission
// Content moderators can add facts directly, others submit for review

import {
  Close as CloseIcon,
  Send as SendIcon,
  UploadFile as UploadFileIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Fade,
  IconButton,
  Paper,
  Popper,
  TextField,
  TextareaAutosize,
  Tooltip,
} from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';
import { useToken } from '../../contexts/tokenContext';
import { useRootStore } from '../../state/useRootStore';
import { ChatMessage } from './ChatMessage';
import { ConversationSelector } from './ConversationSelector';
import { useChat, useWebChat, useWebChatMessages } from './chatHooks';
import { ChatContext } from '../../state/useRootStore';
import { PrimaryTooltip } from '../StyledTooltips';
import { ShowImageModal } from '../modals/ShowImageModal';
import { BlockKitRenderer } from './BlockKitComponents/BlockKitRenderer';
import Markdown from 'react-markdown';
import {
  CodeRenderer,
  ImageRenderer,
  LinkRenderer,
  PreRenderer,
} from './ChatMessageRenderers';
import { replaceLinks } from './ChatMessage';

export function Chat({
  allowFileUpload = false,
  conversationId,
  chatContext,
  header,
}: {
  allowFileUpload?: boolean;
  conversationId?: string;
  chatContext?: ChatContext | null;
  header?: React.ReactNode;
}) {
  const { token } = useToken();
  const [input, setInput] = useState(chatContext?.seedUserMessage || '');
  const [showImageModal, setShowImageModal] = useState(false);
  const [imageModalUrl, setImageModalUrl] = useState<string | undefined>(
    undefined,
  );
  const [didConnect, setDidConnect] = useState(false);
  const [editFactData, setEditFactData] = useState<{
    open: boolean;
    fact: string;
    actionData: any;
  }>({ open: false, fact: '', actionData: null });

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    messages: realtimeMessages,
    isConnected,
    connect,
    disconnect,
    sendMessage,
    sendFeedback,
    sendLearnFactAction,
    resetMessages,
    hintPills,
    feedbackButtons,
    userVotes,
    voteCounts,
    toolCompletionBlocks,
    setToolCompletionBlocks,
    ephemeralUIBlocks,
    ephemeralUIStates,
    setEphemeralUIStates,
    sendEphemeralUIAction,
    ephemeralUIErrors,
    setEphemeralUIErrors,
  } = useChat();

  const { data: webChatMessages, isLoading: isLoadingWebChatMessages } =
    useWebChatMessages(conversationId, chatContext);
  const [currentConversationId, setCurrentConversationId] = useState<
    string | undefined
  >(conversationId);

  useEffect(() => {
    if (isConnected && !didConnect) {
      setDidConnect(true);
      inputRef.current?.focus();
    }
  }, [isConnected]);

  useEffect(() => {
    if (token && !isConnected) {
      connect({ conversationId });
      setCurrentConversationId(conversationId);
    }

    return () => {
      if (isConnected) {
        disconnect();
        resetMessages();
      }
    };
  }, [token, isConnected, connect, disconnect, resetMessages, conversationId]);

  useEffect(() => {
    if (conversationId !== currentConversationId && isConnected) {
      disconnect();
      resetMessages();
      connect({ conversationId });
      setCurrentConversationId(conversationId);
    }
  }, [
    conversationId,
    currentConversationId,
    isConnected,
    connect,
    disconnect,
    resetMessages,
  ]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim().length > 0 && isConnected) {
        sendMessage({ text: input });
        setInput('');
      }
    }
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  const handleImageOpen = (url: string) => {
    setImageModalUrl(url);
    setShowImageModal(true);
  };

  const handleCloseImage = () => {
    setImageModalUrl(undefined);
    setShowImageModal(false);
  };

  useEffect(() => {
    scrollToBottom();
  }, [realtimeMessages, webChatMessages, hintPills]);

  const handleHintClick = (hintValue: string) => {
    if (isConnected) {
      sendMessage({ text: hintValue });
    }
  };

  const handleLearnFactAction = (
    action: 'accept' | 'change' | 'dismiss',
    actionData: any,
    blocks: any[],
  ) => {
    if (!isConnected || !actionData) return;

    if (action === 'dismiss') {
      // Remove the blocks from display
      setToolCompletionBlocks((prev) => {
        const newBlocks = { ...prev };
        Object.keys(newBlocks).forEach((key) => {
          if (newBlocks[key].toolCallId === actionData.tool_call_id) {
            delete newBlocks[key];
          }
        });
        return newBlocks;
      });
      return;
    }

    if (action === 'change') {
      // Find the fact text from the blocks
      const textBlock = blocks.find(
        (b) => b.block_id === 'add_to_brain_msg_block_id',
      );
      const factText = textBlock?.text?.text || '';
      setEditFactData({
        open: true,
        fact: factText,
        actionData: actionData,
      });
      return;
    }

    if (action === 'accept') {
      // Send the accept action via websocket
      sendLearnFactAction('accept', actionData);

      // Remove the blocks from display
      setToolCompletionBlocks((prev) => {
        const newBlocks = { ...prev };
        Object.keys(newBlocks).forEach((key) => {
          if (newBlocks[key].toolCallId === actionData.tool_call_id) {
            delete newBlocks[key];
          }
        });
        return newBlocks;
      });
    }
  };

  const handleFactEditSubmit = () => {
    if (!editFactData.actionData) return;

    // Send the updated fact via websocket
    sendLearnFactAction('change', editFactData.actionData, editFactData.fact);

    // Remove the blocks from display
    setToolCompletionBlocks((prev) => {
      const newBlocks = { ...prev };
      Object.keys(newBlocks).forEach((key) => {
        if (
          newBlocks[key].toolCallId === editFactData.actionData.tool_call_id
        ) {
          delete newBlocks[key];
        }
      });
      return newBlocks;
    });

    // Close the dialog
    setEditFactData({ open: false, fact: '', actionData: null });
  };

  return (
    <div className="flex flex-col h-full">
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {header}

        {conversationId &&
          webChatMessages?.map((message, index) => (
            <ChatMessage
              key={`webchat-${message.ts || index}`}
              token={token}
              handleSendNewUserMessage={(message) =>
                sendMessage({ text: message })
              }
              handleSetInputMessageText={setInput}
              handleOpenImage={(url) => handleImageOpen(url)}
              {...message}
              // Pass feedback props for assistant messages
              feedbackData={
                message.role === 'assistant' && message.ts
                  ? feedbackButtons[message.ts]
                  : undefined
              }
              userVote={message.ts ? userVotes[message.ts] : undefined}
              voteCount={message.ts ? voteCounts[message.ts] : undefined}
              onSendFeedback={
                message.role === 'assistant' && message.ts
                  ? (vote: 'up' | 'down') => sendFeedback(vote, message.ts!)
                  : undefined
              }
            />
          ))}
        {realtimeMessages.map((message) => (
          <ChatMessage
            key={`realtime-${message.ts}`}
            token={token}
            handleSendNewUserMessage={(message) =>
              sendMessage({ text: message })
            }
            handleSetInputMessageText={setInput}
            handleOpenImage={(url) => handleImageOpen(url)}
            {...message}
            // Pass feedback props for assistant messages
            feedbackData={
              message.role === 'assistant' && message.ts
                ? feedbackButtons[message.ts]
                : undefined
            }
            userVote={message.ts ? userVotes[message.ts] : undefined}
            voteCount={message.ts ? voteCounts[message.ts] : undefined}
            onSendFeedback={
              message.role === 'assistant' && message.ts
                ? (vote: 'up' | 'down') => sendFeedback(vote, message.ts!)
                : undefined
            }
          />
        ))}

        {/* Hint Pills - show after the last assistant message */}
        {hintPills.length > 0 && (
          <div className="ml-14 space-y-2">
            <div className="text-sm text-gray-500 italic">
              Ready to explore more? Try one of these:
            </div>
            <div className="flex flex-wrap gap-2">
              {hintPills.map((hint, index) => (
                <Chip
                  key={index}
                  label={hint.text}
                  onClick={() => handleHintClick(hint.value)}
                  clickable
                  variant="outlined"
                  size="small"
                  className="hover:bg-blue-50 hover:border-blue-400"
                />
              ))}
            </div>
          </div>
        )}

        {/* Tool Completion Blocks - show learn a fact UI */}
        {Object.entries(toolCompletionBlocks).map(([ts, data]) => (
          <div
            key={ts}
            className="ml-14 mt-4 p-4 bg-gray-100 rounded-lg max-w-[80%]"
          >
            <div className="space-y-2">
              {data.blocks.map((block: any, index: number) => {
                if (block.type === 'section' && block.text) {
                  if (block.text.type === 'mrkdwn') {
                    // The CSS selector handles \n\n in the markdown:
                    // everything except the first <p> gets a margin-top applied
                    // for better spacing between paragraphs
                    return (
                      <div
                        key={index}
                        className="text-sm [&>p:not(:first-child)]:mt-2"
                      >
                        <Markdown
                          components={{
                            a: LinkRenderer,
                            img: (props) =>
                              ImageRenderer(props, handleImageOpen),
                            code: (props) => CodeRenderer(props),
                            pre: (props) => PreRenderer(props),
                          }}
                        >
                          {replaceLinks(block.text.text)}
                        </Markdown>
                      </div>
                    );
                  } else {
                    return (
                      <div key={index} className="text-sm">
                        {block.text.text}
                      </div>
                    );
                  }
                }
                if (block.type === 'context' && block.elements) {
                  return (
                    <div key={index} className="text-xs text-gray-600">
                      {block.elements[0]?.text}
                    </div>
                  );
                }
                if (block.type === 'divider') {
                  return <hr key={index} className="!my-4" />;
                }
                if (block.type === 'actions') {
                  // Parse the action data from the first button
                  const actionData = block.elements[0]?.value
                    ? JSON.parse(block.elements[0].value)
                    : null;

                  return (
                    <div key={index} className="flex gap-2 mt-3">
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={() =>
                          handleLearnFactAction(
                            'accept',
                            actionData,
                            data.blocks,
                          )
                        }
                      >
                        Accept
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() =>
                          handleLearnFactAction(
                            'change',
                            actionData,
                            data.blocks,
                          )
                        }
                      >
                        Change
                      </Button>
                      <Button
                        variant="outlined"
                        color="error"
                        size="small"
                        onClick={() =>
                          handleLearnFactAction(
                            'dismiss',
                            actionData,
                            data.blocks,
                          )
                        }
                      >
                        Dismiss
                      </Button>
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </div>
        ))}

        {/* Ephemeral UI Blocks - show dynamic forms and UI */}
        {Object.entries(ephemeralUIBlocks).map(([ts, data]) => (
          <div
            key={ts}
            className="ml-14 mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-[80%]"
          >
            <BlockKitRenderer
              blocks={data.blocks}
              uiId={data.uiId}
              messageTs={Number(ts)}
              formState={ephemeralUIStates[data.uiId] || {}}
              validationErrors={ephemeralUIErrors[data.uiId] || []}
              onFormStateChange={(newState) => {
                setEphemeralUIStates((prev) => ({
                  ...prev,
                  [data.uiId]: newState,
                }));
                // Clear validation errors when form state changes
                if (ephemeralUIErrors[data.uiId]) {
                  setEphemeralUIErrors((prev) => {
                    const newErrors = { ...prev };
                    delete newErrors[data.uiId];
                    return newErrors;
                  });
                }
              }}
              onSubmit={(formData) => {
                sendEphemeralUIAction(
                  'submit',
                  data.uiId,
                  Number(ts),
                  formData,
                );
              }}
              onCancel={() => {
                sendEphemeralUIAction('cancel', data.uiId, Number(ts));
              }}
            />
          </div>
        ))}
      </div>
      <div className="border-t p-4 flex items-center gap-2">
        {allowFileUpload && (
          <Button variant="text" size="small" disabled={!isConnected}>
            <UploadFileIcon />
          </Button>
        )}
        <div className="flex-1 flex items-center gap-2 bg-[hsl(240,4.8%,95.9%)] rounded-lg px-4 py-2">
          <TextField
            placeholder={isConnected ? 'Type your message' : 'Connecting...'}
            className="flex-1"
            multiline
            maxRows={3}
            variant="standard"
            value={input}
            disabled={!isConnected}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            InputProps={{
              disableUnderline: true,
            }}
            inputRef={inputRef}
          />
        </div>
        <Button
          size="small"
          variant="text"
          disabled={!isConnected || input.trim().length === 0}
          onClick={() => {
            sendMessage({ text: input });
            setInput('');
          }}
          sx={{ minWidth: 'initial' }}
        >
          <SendIcon />
        </Button>
      </div>

      <ShowImageModal
        showImageModal={showImageModal}
        setShowImageModal={setShowImageModal}
        handleCloseImage={handleCloseImage}
        imageModalUrl={imageModalUrl}
      />

      <Dialog
        open={editFactData.open}
        onClose={() =>
          setEditFactData({ open: false, fact: '', actionData: null })
        }
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit Fact</DialogTitle>
        <DialogContent>
          <TextareaAutosize
            minRows={4}
            style={{ width: '100%', padding: '8px', fontFamily: 'inherit' }}
            value={editFactData.fact}
            onChange={(e) =>
              setEditFactData((prev) => ({ ...prev, fact: e.target.value }))
            }
            placeholder="Enter the fact to be learned..."
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() =>
              setEditFactData({ open: false, fact: '', actionData: null })
            }
          >
            Cancel
          </Button>
          <Button
            onClick={handleFactEditSubmit}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

function ChatButton({
  onClick,
}: {
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
}) {
  return (
    <button
      className="relative w-8 h-8 rounded-full"
      onClick={onClick}
      aria-label="chat"
      style={{
        boxShadow:
          '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)',
      }}
    >
      <img
        src="/critter_glasses.png"
        alt="Chat"
        className="w-full h-full object-cover"
      />
    </button>
  );
}

export function ChatFab({ isExpanded }: { isExpanded: boolean }) {
  const { data: webChatEnabled, isLoading } = useWebChat();
  const [isOpen, setIsOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [conversationId, setConversationId] = useState<string | undefined>(
    undefined,
  );
  const [chatKey, setChatKey] = useState(0);
  const hiddenPopperRef = useRef(null);

  const handleClick = (event: any) => {
    setAnchorEl(hiddenPopperRef.current);
    setIsOpen((prev) => !prev);
  };

  const startNewChat = () => {
    setConversationId(undefined);
    setChatKey((prevKey) => prevKey + 1);
  };

  const selectConversation = (id: string) => {
    setConversationId(id);
    setChatKey((prevKey) => prevKey + 1);
  };

  if (isLoading || !webChatEnabled) {
    return null;
  }

  const chatButton = (
    <Box>
      <ChatButton onClick={handleClick} />
    </Box>
  );

  return (
    <>
      {isExpanded ? (
        <PrimaryTooltip title="Hello! Click here to Chat with Tribble">
          {chatButton}
        </PrimaryTooltip>
      ) : (
        <Tooltip
          title="Hello! Click here to Chat with Tribble"
          placement="right"
        >
          {chatButton}
        </Tooltip>
      )}
      <Box
        ref={hiddenPopperRef}
        sx={{ position: 'fixed', bottom: 4, right: 12 }}
      ></Box>
      <Popper
        open={isOpen}
        anchorEl={anchorEl}
        placement="top-end"
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
        ]}
        className="!z-[2000]"
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper
              elevation={3}
              sx={{
                height: '60vh',
                maxWidth: '600px',
                width: '100vw',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <div className="flex justify-between items-center p-4">
                <h2 className="text-xl font-semibold">Chat with Tribble</h2>
                <div>
                  <ConversationSelector
                    onNewChat={startNewChat}
                    onSelectConversation={selectConversation}
                  />
                  <IconButton size="small" onClick={() => setIsOpen(false)}>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <Chat key={chatKey} conversationId={conversationId} />
              </div>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  );
}

export function ChatPanel() {
  const rootStore = useRootStore();

  return (
    <div className="flex flex-col justify-between h-full">
      <div className="flex flex-col py-4 px-6 bg-[#1a56db]">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">
            Chat with Tribble
          </h2>
          <div>
            <IconButton
              size="small"
              onClick={() => rootStore.closeChatSession()}
              sx={{ color: '#ffffffdd', mr: '-8px' }}
            >
              <CloseIcon />
            </IconButton>
          </div>
        </div>
      </div>
      <div className="flex-col flex-1 overflow-hidden">
        <Chat
          conversationId={String(rootStore.chatSession?.conversationId ?? '')}
          chatContext={rootStore.chatSession}
          header={
            <Divider>
              <Chip
                label="Ask any follow-up or clarifying questions here."
                variant="outlined"
                size="small"
                color="default"
                sx={{ px: 2, my: 1 }}
              />
            </Divider>
          }
        />
      </div>
    </div>
  );
}
