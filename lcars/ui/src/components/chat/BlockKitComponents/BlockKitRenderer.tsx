// Supports headers, sections, inputs, actions, dividers, and context blocks

import React from 'react';
import { Box, Typography } from '@mui/material';
import { BlockKitInput } from './BlockKitInput';
import { BlockKitSelect } from './BlockKitSelect';
import { BlockKitCheckbox } from './BlockKitCheckbox';
import { BlockKitRadio } from './BlockKitRadio';
import { BlockKitActions } from './BlockKitActions';
import {
  UIBlock,
  FormState,
  InputBlock,
  TextInputElement,
  StaticSelectElement,
  CheckboxesElement,
  RadioButtonsElement,
} from './types';
import { BlockKitSection } from './BlockKitSection';

interface BlockKitRendererProps {
  blocks: UIBlock[];
  uiId: string;
  messageTs: number;
  formState: FormState;
  onFormStateChange: (newState: FormState) => void;
  onSubmit: (formData: FormState) => void;
  onCancel: () => void;
  validationErrors?: string[];
}

export const BlockKitRenderer: React.FC<BlockKitRendererProps> = ({
  blocks,
  uiId,
  messageTs,
  formState,
  onFormStateChange,
  onSubmit,
  onCancel,
  validationErrors = [],
}) => {
  const handleFieldChange = (fieldName: string, value: string | string[]) => {
    onFormStateChange({
      ...formState,
      [fieldName]: value,
    });
  };

  const renderBlock = (block: UIBlock, index: number) => {
    switch (block.type) {
      case 'header':
        return (
          <Typography key={index} variant="h6" gutterBottom>
            {block.text?.text || ''}
          </Typography>
        );

      case 'markdown':
      case 'section':
        if (block.text && block.text !== 'Interactive message') {
          return <BlockKitSection block={block} />;
        }
        return null;

      case 'divider':
        return (
          <Box
            key={`divider_${index}`}
            sx={{ borderBottom: '1px solid #e0e0e0', my: 2 }}
          />
        );

      case 'context':
        if (block.elements?.[0].type === 'image') {
          return (
            <img
              src={block.elements[0].image_url}
              alt=""
              key={`webchat_context_${block.block_id || `img_${index}`}_${index}`}
              style={{ maxWidth: '100%' }}
            />
          );
        } else {
          return (
            <Typography
              key={index}
              variant="caption"
              color="text.secondary"
              paragraph
            >
              {block.elements?.[0]?.text || ''}
            </Typography>
          );
        }

      case 'input':
        const inputBlock = block as InputBlock;
        const element = inputBlock.element;
        const blockId = inputBlock.block_id || `block_${index}`;

        if (element.type === 'plain_text_input') {
          const plainTextElement = element as TextInputElement;
          return (
            <BlockKitInput
              key={blockId}
              block={inputBlock}
              value={(formState[plainTextElement.action_id] as string) || ''}
              onChange={(value) =>
                handleFieldChange(plainTextElement.action_id, value)
              }
            />
          );
        }

        if (element.type === 'static_select') {
          const selectElement = element as StaticSelectElement;
          return (
            <BlockKitSelect
              key={blockId}
              block={inputBlock}
              value={(formState[selectElement.action_id] as string) || ''}
              onChange={(value) =>
                handleFieldChange(selectElement.action_id, value)
              }
            />
          );
        }

        if (element.type === 'checkboxes') {
          const checkboxElement = element as CheckboxesElement;
          return (
            <BlockKitCheckbox
              key={blockId}
              block={inputBlock}
              value={(formState[checkboxElement.action_id] as string[]) || []}
              onChange={(value) =>
                handleFieldChange(checkboxElement.action_id, value)
              }
            />
          );
        }

        if (element.type === 'radio_buttons') {
          const radioElement = element as RadioButtonsElement;
          return (
            <BlockKitRadio
              key={blockId}
              block={inputBlock}
              value={(formState[radioElement.action_id] as string) || ''}
              onChange={(value) =>
                handleFieldChange(radioElement.action_id, value)
              }
            />
          );
        }

        return null;

      case 'actions':
        return (
          <BlockKitActions
            key={index}
            block={block}
            onSubmit={() => onSubmit(formState)}
            onCancel={onCancel}
          />
        );

      default:
        return null;
    }
  };

  const renderedValidationErrors = () => {
    if (validationErrors.length === 0) return null;

    return (
      <Box sx={{ color: 'error.main', my: 2 }}>
        {validationErrors.map((error, idx) => (
          <Typography key={idx} variant="body2">
            {error}
          </Typography>
        ))}
      </Box>
    );
  };

  return (
    <Box className="block-kit-renderer">
      {renderedValidationErrors()}
      {blocks.map((block, index) => renderBlock(block, index))}
      {renderedValidationErrors()}
    </Box>
  );
};
