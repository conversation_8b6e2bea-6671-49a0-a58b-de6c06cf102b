// Renders submit and cancel buttons for ephemeral UI forms

import React from 'react';
import { Box, Button } from '@mui/material';
import { ActionsBlock, ButtonElement } from './types';

interface BlockKitActionsProps {
  block: ActionsBlock;
  onSubmit: () => void;
  onCancel: () => void;
}

export const BlockKitActions: React.FC<BlockKitActionsProps> = ({
  block,
  onSubmit,
  onCancel,
}) => {
  const elements = (block.elements as ButtonElement[]) || [];

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 3 }}>
      {elements.map((element, index: number) => {
        if (element.type !== 'button') return null;

        const isSubmit = element.action_id === 'ephemeral_ui_submit';
        const isCancel = element.action_id === 'ephemeral_ui_cancel';
        const isPrimary = element.style === 'primary';

        if (isSubmit) {
          return (
            <Button
              key={index}
              variant={isPrimary ? 'contained' : 'outlined'}
              color="primary"
              onClick={onSubmit}
            >
              {element.text?.text || 'Submit'}
            </Button>
          );
        }

        if (isCancel) {
          return (
            <Button
              key={index}
              variant="outlined"
              color="error"
              onClick={onCancel}
            >
              {element.text?.text || 'Cancel'}
            </Button>
          );
        }

        // For other buttons, just render them without actions for now
        return (
          <Button
            key={index}
            variant={isPrimary ? 'contained' : 'outlined'}
            disabled
          >
            {element.text?.text || 'Button'}
          </Button>
        );
      })}
    </Box>
  );
};
