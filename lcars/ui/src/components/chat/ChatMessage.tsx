import { useAuth0 } from '@auth0/auth0-react';
import { Ava<PERSON>, Tooltip, IconButton } from '@mui/material';
import {
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
} from '@mui/icons-material';
import React, { PropsWithChildren, useEffect, useState } from 'react';
import Markdown from 'react-markdown';
import { UserAvatar } from '../UserAvatar';
import { cn } from '../../utils/tailwinds';
import { useRootStore } from '../../state/useRootStore';
import { ActionBarQuestionnaireQuestion } from './AssistantMessageActionBar';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
  PreRenderer,
} from './ChatMessageRenderers';

// type div props with text
type AnimatedMessageProps = React.ComponentProps<'div'> & {
  text: string;
  typingSpeed?: number;
};

// Animated typing effect component
export function AnimatedMessage({
  text,
  typingSpeed = 20,
  ...props
}: AnimatedMessageProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (text.startsWith(displayedText)) {
      setCurrentIndex(displayedText.length);
    } else {
      setDisplayedText('');
      setCurrentIndex(0);
    }
  }, [text]);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, currentIndex + 1));
        setCurrentIndex((prevIndex) => prevIndex + 1);
      }, typingSpeed);

      return () => clearTimeout(timer);
    }
  }, [text, currentIndex, typingSpeed]);

  return <div {...props}>{displayedText}</div>;
}

export function ChatMessage({
  text,
  role,
  messageType,
  token,
  userId,
  userName,
  noAnimate = false,
  isLoading = false,
  handleSendNewUserMessage,
  handleSetInputMessageText,
  handleOpenImage,
  feedbackData,
  userVote,
  voteCount,
  onSendFeedback,
}: {
  text: string;
  role: string;
  messageType?: string;
  token: string;
  userId?: number;
  userName?: string;
  noAnimate?: boolean;
  isLoading?: boolean;
  handleSendNewUserMessage: (message: string) => void;
  handleSetInputMessageText: (text: string) => void;
  handleOpenImage?: (url: string) => void;
  feedbackData?: {
    conversationDetailId: number;
    conversationId: number;
  };
  userVote?: 'up' | 'down';
  voteCount?: {
    upVotes: number;
    downVotes: number;
  };
  onSendFeedback?: (vote: 'up' | 'down') => void;
}) {
  const user = useAuth0().user;
  const rootStore = useRootStore();
  const isQuestionnaireChat = !!rootStore.chatSession?.contentDetailId;
  const isThinkingOrToolMessage =
    messageType === 'thinking' || messageType === 'tool';

  const preprocessedText = replaceLinks(text);

  return (
    <>
      <div
        className={cn(`flex items-start gap-4`, {
          'justify-end': role === 'user',
        })}
      >
        {role === 'assistant' && (
          <Avatar src="/critter_glasses.png" alt="Assistant Avatar">
            Bot
          </Avatar>
        )}
        <div className="flex flex-col max-w-[80%]">
          <div
            className={cn('rounded-lg p-3 min-w-[225px] ', {
              'bg-[#1a56db]': role === 'user',
              'bg-[hsl(240,4.8%,95.9%)]': role === 'assistant',
            })}
          >
            <div
              className={cn(
                'prose prose-sm break-words [&>hr]:my-6 leading-[1.6]',
                {
                  'text-white': role === 'user',
                  'text-grey-800': role === 'assistant',
                },
              )}
            >
              <Markdown
                components={{
                  a: LinkRenderer,
                  img: (props) =>
                    !isLoading ? (
                      ImageRenderer(props, handleOpenImage)
                    ) : (
                      // Show placeholder image to prevent flickering
                      //   and excessive image fetching as the component updates
                      <span className="inline-block animate-pulse bg-gray-200 rounded border-2 border-gray-200 min-h-[120px] min-w-[200px]" />
                    ),
                  code: (props) => CodeRenderer(props),
                  pre: (props) => PreRenderer(props),
                }}
              >
                {preprocessedText}
              </Markdown>
            </div>
            {/* 
            {
              role === 'assistant' && !noAnimate 
                ? <AnimatedMessage className="text-sm" text={text}/> 
                : <div className="text-sm">{text}</div>
            }
            */}
          </div>

          {role === 'assistant' &&
            isQuestionnaireChat &&
            !isThinkingOrToolMessage && (
              <ActionBarQuestionnaireQuestion
                token={token}
                answer={text}
                handleSendNewUserMessage={handleSendNewUserMessage}
                handleSetInputMessageText={handleSetInputMessageText}
              />
            )}
        </div>
        {role === 'user' && (
          <Tooltip title={userName ?? user?.name!} placement="bottom-end">
            <div>
              <UserAvatar
                name={userName ?? user?.name!}
                sx={{
                  color: '#1a56db80',
                  bgcolor: '#fff',
                  border: '2px solid #1a56db80',
                  height: '32px',
                  width: '32px',
                  fontSize: '1rem',
                }}
              />
            </div>
          </Tooltip>
        )}
      </div>

      {/* Feedback Buttons - render below assistant messages */}
      {role === 'assistant' && feedbackData && onSendFeedback && (
        <div className="flex gap-4 ml-14 items-center mt-2">
          <div className="flex flex-row items-center">
            <Tooltip
              title={
                userVote === 'up'
                  ? 'Click again to remove your vote'
                  : userVote === 'down'
                    ? 'You found this unhelpful'
                    : 'This was helpful'
              }
            >
              <IconButton
                size="small"
                onClick={() => onSendFeedback('up')}
                className={
                  userVote === 'up'
                    ? 'text-green-600 bg-green-100'
                    : 'hover:bg-green-100'
                }
                disabled={userVote === 'down'}
              >
                <ThumbUpIcon
                  fontSize="small"
                  sx={{ color: userVote === 'up' ? 'green' : 'inherit' }}
                />
              </IconButton>
            </Tooltip>
            <span
              className={`text-sm min-w-[1.5rem] text-center ${
                userVote === 'up' ? 'text-green-800 font-bold' : 'text-gray-500'
              }`}
            >
              {voteCount?.upVotes || 0}
            </span>
          </div>

          <div className="flex flex-row items-center">
            <Tooltip
              title={
                userVote === 'down'
                  ? 'Click again to remove your vote'
                  : userVote === 'up'
                    ? 'You found this helpful'
                    : "This wasn't helpful"
              }
            >
              <IconButton
                size="small"
                onClick={() => onSendFeedback('down')}
                className={
                  userVote === 'down'
                    ? 'text-red-600 bg-red-100'
                    : 'hover:bg-red-100'
                }
                disabled={userVote === 'up'}
              >
                <ThumbDownIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <span
              className={`text-sm min-w-[1.5rem] text-center text-gray-500 ${
                userVote === 'down' ? 'font-bold' : ''
              }`}
            >
              {voteCount?.downVotes || 0}
            </span>
          </div>
        </div>
      )}
    </>
  );
}

// Use this to create a chat-looking messsage.
type ChatMessageContainerProps = PropsWithChildren<{
  role: string;
  userName?: string;
}>;

export const ChatMessageContainer: React.FC<ChatMessageContainerProps> = ({
  role,
  userName,
  children,
}) => {
  return (
    <div
      className={cn(`flex items-start gap-4`, {
        'justify-end': role === 'user',
      })}
    >
      {role === 'assistant' && (
        <Avatar src="/critter_glasses.png" alt="Assistant Avatar">
          Bot
        </Avatar>
      )}
      <div className="flex flex-col max-w-[80%]">
        <div
          className={cn('rounded-lg p-3 min-w-[225px] ', {
            'bg-[#1a56db]': role === 'user',
            'bg-[hsl(240,4.8%,95.9%)]': role === 'assistant',
          })}
        >
          <div
            className={cn('prose prose-sm break-words	[&>hr]:my-6', {
              'text-white': role === 'user',
              'text-grey-800': role === 'assistant',
            })}
          >
            {children}
          </div>
        </div>
      </div>
      {role === 'user' && (
        <Tooltip title={userName ?? 'User'} placement="bottom-end">
          <div>
            <UserAvatar
              name={userName ?? 'User'}
              sx={{
                color: '#1a56db80',
                bgcolor: '#fff',
                border: '2px solid #1a56db80',
                height: '32px',
                width: '32px',
                fontSize: '1rem',
              }}
            />
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export function replaceLinks(text: string): string {
  const markdownFileRegex =
    /\[([^\[\]]*)\]\(((https:\/\/[^)]*?\.files\.tribble\.ai)\/(.*?)\/(.*?\.(pptx|pdf|docx|xlsx|doc|xls|ppt)))\)/g;
  // Replace markdown links with HTML anchor tags
  if (!text) return text;

  return text.replace(
    markdownFileRegex,
    (match, linkText, fullUrl, blobUrl, container, filename) => {
      // Replace the files.tribble.ai domain with current path + /api/files/
      const newUrl = fullUrl.replace(
        /https:\/\/[^\/]*\.files\.tribble\.ai\/documents\//,
        `${window.location.origin}/api/files?file_id=`,
      );
      return `[${linkText}](${newUrl})`;
    },
  );
}
