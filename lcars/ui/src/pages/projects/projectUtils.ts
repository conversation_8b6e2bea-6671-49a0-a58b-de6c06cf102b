import colors from 'tailwindcss/colors';
import {
  processAnswerCitations,
  reorderCitations,
} from '../knowledge/knowledgeUtils';
import {
  ContentSourceDoc,
  ContentSourceQA,
  Grouping,
  MappedAnswerEntry,
  ProjectContent,
  ProjectContentStatus,
  QuestionDto,
  QuestionnaireDto,
  Rfp,
  RfpStatusValue,
} from './model';
import { MetadataFilterValue } from '../../model';
import {
  createNewConversationForAnswerEntry,
  createNewConversationForQuestion,
} from './projectHooks';
import { RootStore } from '../../state/useRootStore';
import { AnswerEntry } from '../../../../src/model/e2e';

// A lot of this code is replicated from Holodeck. At some point, this code
// and Holodeck code will go through a revamp as the disparate data models
// between questionnaires and E2E RFPs are...not great.

export const QUESTIONNAIRE_ERROR_MAP = (
  projectContent: ProjectContent | undefined,
) => {
  return {
    NO_QUESTIONS: 'No questions were found during analysis',
    DEFAULT: `${projectContent?.longForm ? 'RFP' : 'Questionnaire'} is taking too long to process. If you think this isn't an error, please continue waiting for the ${projectContent?.longForm ? 'RFP' : 'Questionnaire'} to finish.`,
  };
};

export const processRfps = (
  allQuestionnaires: QuestionnaireDto[],
  rfps: Rfp[],
  unfinishedRfps: Rfp[],
) => {
  const unfinished = unfinishedRfps
    // Filter out parents, show children only
    .filter(
      (r) => !unfinishedRfps.some((u) => u.details.parentContentId === r.id),
    )
    .filter((r) => r.rfp_status !== 'error');

  // Filter out any questionnaires that are already in completed E2E RFP list
  // (i.e. the Answer RFP "step 2" process)
  const filteredQuestionnaires = allQuestionnaires.filter(
    (q) =>
      !rfps.some((r) => r.details.rfpId === q.details?.rfpId) &&
      !unfinishedRfps.some(
        (r) =>
          r.details.parentContentId !== undefined &&
          r.details.parentContentId === q.details?.parentContentId,
      ),
  );

  return {
    rfps: filteredQuestionnaires,
    e2eRfps: [...unfinished, ...rfps],
  };
};

// export const mapRfpsToProjects = (
//   rfps: QuestionnaireDto[],
//   availableUsers: LimitedUser[],
// ): ProjectContent[] => {
//   return rfps.map((rfp) => {
//     const owner = rfp.assignees?.find((user) => user.is_owner);
//     const ownerUser = owner
//       ? availableUsers.find((user) => user.id == owner.assignee_id)
//       : undefined;

//     return {
//       id: Number(rfp.content_id),
//       type: 'questionnaire',
//       label: rfp.label ?? rfp.file_name,
//       description: rfp.details?.project_description,
//       status: mapRfpStatus(rfp.rfp_status),
//       statusLabel: rfp.rfp_status_label,
//       dueDate: processUTCDate(rfp.due_date),
//       createdDate: getQuestionnaireCreationDate(rfp),
//       customerName: rfp.details?.customer_name,
//       metadataFilter: rfp.metadata_filter,
//       owner: ownerUser,
//       numQuestions: Number(rfp.num_questions) ?? 0,
//       numReviewed: Number(rfp.num_questions_accepted) ?? 0,
//       documentId: Number(rfp.document_id),
//       jobId: Number(rfp.job_id),
//       isE2E: !!rfp.details?.rfpId,
//       rfpId: rfp.details?.rfpId,
//       details: rfp.details,
//       customInstructions: rfp.details?.custom_instructions,
//     };
//   });
// };

// export const mapE2eRfpsToProjects = (
//   e2eRfps: Rfp[],
//   availableUsers: LimitedUser[],
// ): ProjectContent[] => {
//   return e2eRfps.map((e2e) => {
//     let projectLabel;
//     if (e2e.details.project_name) {
//       projectLabel = e2e.details.project_name;
//     } else if (e2e.details.fileName) {
//       projectLabel = e2e.details.fileName;
//     } else if (e2e.details.files?.length > 0) {
//       projectLabel = e2e.details.files[0].name;
//     }

//     let owner = e2e.assignees?.find((user) => user.is_owner);
//     const ownerId = owner ? owner.assignee_id : e2e.details.agentParams.userId;
//     const ownerUser = ownerId
//       ? availableUsers.find((user) => user.id == ownerId)
//       : undefined;

//     return {
//       id: Number(e2e.id),
//       type: 'e2e',
//       label: projectLabel,
//       description: e2e.details?.project_description,
//       status: mapRfpStatus(e2e.rfp_status),
//       statusLabel: e2e.rfp_status_label,
//       dueDate: processUTCDate(e2e.due_date),
//       createdDate: getQuestionnaireCreationDate(e2e),
//       lastUpdated: getProjectRfxUpdated(e2e),
//       customerName: e2e.details?.customer_name,
//       metadataFilter: e2e.metadata_filter,
//       owner: ownerUser,
//       longForm: e2e.details.agentParams.longForm === true,
//       jobId: e2e.job_id ? Number(e2e.job_id) : undefined,
//       isE2E: true,
//       rfpId: e2e.details?.rfpId,
//       rfxStatusMessage: e2e.rfx_status_message,
//       details: e2e.details,
//       customInstructions: e2e.details?.custom_instructions,
//     };
//   });
// };

export const mapRfpStatus = (
  status: RfpStatusValue | undefined,
): ProjectContentStatus => {
  switch (status?.toLowerCase()) {
    case 'analyzing':
    case 'generating':
      return 'In Analysis';
    case 'long_form_go_no_go':
      return 'Pending';
    case 'longform_outline_review':
      return 'Pending';
    case 'spreadsheet_confirm_analysis':
      return 'Pending';
    case 'in_review':
      return 'In Review';
    case 'finished':
      return 'Finished';
    case 'rejected':
      return 'Rejected';
    case 'error':
      return 'Error';
    default:
      // Shouldn't happen
      return 'In Analysis';
  }
};

export const STATUS_COLOR_MAP = {
  'In Analysis': {
    colors: {
      backgroundColor: colors.orange[500] + '15',
      color: colors.orange[500],
    },
  },
  Pending: {
    colors: {
      backgroundColor: colors.red[500] + '15',
      color: colors.red[500],
    },
  },
  'In Review': {
    colors: {
      backgroundColor: colors.purple[500] + '15',
      color: colors.purple[500],
    },
  },
  Finished: {
    colors: {
      backgroundColor: colors.green[500] + '15',
      color: colors.green[500],
    },
  },
  Rejected: {
    colors: {
      backgroundColor: colors.red[700] + '15',
      color: colors.red[700],
    },
  },
  All: {
    colors: {
      backgroundColor: colors.purple[500] + '15',
      color: colors.purple[500],
    },
  },
  Active: {
    colors: {
      backgroundColor: colors.blue[500] + '15',
      color: colors.blue[500],
    },
  },
  default: {
    colors: {
      backgroundColor: colors.gray[500] + '15',
      color: colors.gray[500],
    },
  },
};

export const getStatusColor = (
  status: RfpStatusValue,
): {
  colors: {
    backgroundColor: string;
    color: string;
  };
} => {
  switch (status?.toLowerCase()) {
    case 'analyzing':
    case 'generating':
    case 'generating_outline':
    case 'generating_response':
      return {
        colors: {
          backgroundColor: colors.orange[500] + '15',
          color: colors.orange[500],
        },
      };
    case 'long_form_go_no_go':
    case 'spreadsheet_confirm_analysis':
    case 'in_review_outline':
      return {
        colors: {
          backgroundColor: colors.red[500] + '15',
          color: colors.red[500],
        },
      };
    case 'in_review':
    case 'in_review_response':
      return {
        colors: {
          backgroundColor: colors.purple[500] + '15',
          color: colors.purple[500],
        },
      };
    case 'finished':
      return {
        colors: {
          backgroundColor: colors.green[500] + '15',
          color: colors.green[500],
        },
      };
    case 'rejected':
      return {
        colors: {
          backgroundColor: colors.red[700] + '15',
          color: colors.red[700],
        },
      };
    default:
      return {
        colors: {
          backgroundColor: colors.gray[500] + '15',
          color: colors.gray[500],
        },
      };
  }
};

export type ConfidenceScore = {
  value: string;
  label: string;
  menuLabel: string;
  color: string;
  scoreMin: number;
  scoreMax: number;
};

export const CONFIDENCE_SCORE_LIST: ConfidenceScore[] = [
  {
    value: 'all',
    label: 'All',
    menuLabel: 'All',
    color: colors.blue[600],
    scoreMin: -999,
    scoreMax: 999,
  },
  {
    value: 'high',
    label: 'High Confidence',
    menuLabel: 'High',
    color: colors.green[600],
    scoreMin: 3,
    scoreMax: 999,
  },
  {
    value: 'medium',
    label: 'Medium Confidence',
    menuLabel: 'Medium',
    color: colors.yellow[600],
    scoreMin: 2,
    scoreMax: 2,
  },
  {
    value: 'low',
    label: 'Low Confidence',
    menuLabel: 'Low',
    color: colors.amber[600],
    scoreMin: 1,
    scoreMax: 1,
  },
  {
    value: 'couldNotAnswer',
    label: 'No Answer',
    menuLabel: 'No Answer',
    color: colors.red[600],
    scoreMin: -999,
    scoreMax: 0,
  },
  {
    value: 'userModified',
    label: 'User Modified',
    menuLabel: 'User Modified',
    color: colors.green[600],
    scoreMin: -999,
    scoreMax: 999,
  },
];

export type AnswerState = {
  value: string;
  label: string;
};

export const ANSWER_STATE_LIST: AnswerState[] = [
  {
    value: 'all',
    label: 'All',
  },
  {
    value: 'original',
    label: 'Tribble Generated',
  },
  {
    value: 'modified',
    label: 'User Modified',
  },
];

export type ReviewStatus = {
  value: string;
  label: string;
  menuLabel: string;
};

export const REVIEW_STATUS_LIST: ReviewStatus[] = [
  {
    value: 'all',
    label: 'All',
    menuLabel: 'All',
  },
  {
    value: 'reviewed',
    label: 'Yes',
    menuLabel: 'Reviewed',
  },
  {
    value: 'not_reviewed',
    label: 'No',
    menuLabel: 'Not Reviewed',
  },
];

export function processUTCDate(
  inputDate: string | Date | undefined,
): Date | undefined {
  if (!inputDate) return undefined;

  let date: Date;

  if (typeof inputDate === 'string') {
    // Use the native Date constructor which handles ISO 8601 strings correctly.
    date = new Date(inputDate);
  } else if (inputDate instanceof Date) {
    // If it's already a Date object, use it directly without stripping time.
    date = inputDate;
  } else {
    // Should not be reachable with TypeScript but good practice
    return undefined;
  }

  // Check if parsing resulted in an invalid date
  if (isNaN(date.getTime())) {
    // Optional: Log a warning if needed
    // console.warn(`Could not parse date value: ${inputDate}`);
    return undefined;
  }

  return date;
}

/**
 * Displays a business date consistently for all users globally, regardless of timezone.
 * This function shows the same calendar date (e.g., "July 1, 2025") for all users,
 * which is the correct behavior for project due dates.
 * @param {Date | undefined} date - The input Date object (stored as UTC in database).
 * @returns {string | undefined} - Formatted date string using default locale conventions (e.g., "12/25/2025", "25/12/2025", etc.) or undefined.
 */
export function displayUTCDateWithDefaultLocale(
  date: Date | undefined,
): string | undefined {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return undefined; // Handle invalid inputs
  }

  try {
    // For business dates, we want to display the UTC date components consistently
    // This ensures July 1st appears as "July 1st" for all users globally
    return date.toLocaleDateString(undefined, {
      timeZone: 'UTC', // Force UTC to show business date consistently
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      // Note: We only display date components since due dates are business dates
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return undefined;
  }
}

export const handleDownloadAsCSV = (
  questions: QuestionDto[],
  fileName: string,
  inlineCitations: boolean = false,
) => {
  const qaList = getQuestionsAndAnswers(questions, inlineCitations);

  const keys = Object.keys(qaList[0]);
  const csvContents =
    `${keys.map((key) => key.replace(/[_]/g, ' ')).join(',')}\n` +
    qaList
      .map((qa: any) => {
        return keys
          .map((k) => {
            let cell = qa[k];
            if (!cell) return '';
            cell.replace(/"/g, '""');
            if (cell.search(/("|,|\n)/g) >= 0) {
              cell = `"${cell}"`;
            }
            return cell;
          })
          .join(',');
      })
      .join('\n');

  const csvData = new Blob([csvContents], { type: 'text/csv' });
  const csvUrl = URL.createObjectURL(csvData);
  const link = document.createElement('a');

  link.setAttribute('href', csvUrl);
  link.setAttribute('download', fileName.replace(/\s+/g, '_') + '.csv');
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();

  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(csvUrl);
};

export const hasDisplayQuestion = (question: QuestionDto) => {
  return (
    question.input?.meta && question.input?.meta['displayQuestion']?.length > 0
  );
};

// Account for displayQuestion populated via E2E RFP Spreadsheets
export const questionText = (question: QuestionDto) => {
  return hasDisplayQuestion(question)
    ? question.input?.meta?.['displayQuestion']
    : question.input.query_string;
};

export const getQuestionsAndAnswers = (
  questions: QuestionDto[],
  inlineCitations: boolean = false,
) => {
  const hasGroupings = questions.some(
    (question) => question.grouping_id !== null,
  );

  return questions.map((question) => {
    const isRegenerated =
      (question.input?.regenerated_query_string !== undefined &&
        question.input?.regenerated_query_string != '' &&
        question.input?.regenerated_query_string !=
          question.input.query_string) ||
      (question.input?.regenerated_extra_context !== undefined &&
        question.input?.regenerated_extra_context != '');

    const originalQuestionText = questionText(question);

    const processedQuestion = isRegenerated
      ? question.input?.regenerated_query_string || originalQuestionText
      : originalQuestionText;

    const generatedAnswer = getGeneratedAnswer(question, inlineCitations);

    const translatedText =
      question.output_original?.translation?.translation ?? '';

    const processedAnswer = shouldUseTranslatedAnswer(question)
      ? processAnswerCitations(translatedText, true)
      : isModifiedAnswer(question)
        ? question.output_modified!.text
        : processAnswerCitations(generatedAnswer, true);

    // Just replace all quotes with single quotation marks so CSV parsers can handle.
    let details = {
      question_number: String(question.index + 1),
    };

    if (hasGroupings) {
      details['grouping'] = question.grouping_name || '';
    }

    return {
      ...details,
      question: processedQuestion.replace(/"/g, "'"),
      answer: processedAnswer.replace(/"/g, "'"),
      confidence: isModifiedAnswer(question)
        ? 'Modified Answer'
        : getAnswerConfidence(question),
    };
  });
};

export const getMultiVersionAnswers = (question: QuestionDto | any) => {
  return question.output_original?.answer_versions !== undefined
    ? question.output_original?.answer_versions.reduce((agg, answer, idx) => {
        const isMultiTypeMDF = answer?.metadata_filter?.multiple_types;
        const mdfMultiTypeLabel = isMultiTypeMDF
          ? ' (' +
            answer?.metadata_filter?.multiple_types.reduce(
              (acc, mdf_type, idx) => {
                return (
                  acc +
                  `${idx > 0 ? ', ' : ''}${mdf_type.type_name}: ${
                    mdf_type.value
                  }`
                );
              },
              '',
            ) +
            ')'
          : '';

        const answerLabel = isMultiTypeMDF
          ? `Answer Version ${idx + 1}${mdfMultiTypeLabel}:\n\n`
          : `${answer?.metadata_filter.value}: `;

        agg.push(answerLabel + (answer.answer || ''));
        return agg;
      }, [])
    : [];
};

// See spreadsheet-service/src/spreadsheet_service.ts
// This is the same logic for handling inline citations
const getAnswerWithInlineCitations = (
  question: QuestionDto,
  inlineCitations: boolean,
) => {
  const originalAnswer = question.output_original!.answer || '';
  if (!inlineCitations) {
    return originalAnswer;
  }

  const outputOriginal = question.output_original;
  const outputModified = question.output_modified;

  const isModifiedAnswer = outputModified?.text && outputModified.text !== '';
  if (isModifiedAnswer) {
    return outputModified.text;
  }

  const docSources = outputOriginal?.documents?.docs || [];
  const qaSources = outputOriginal?.documents?.qa || [];
  const numSources = docSources.length + qaSources.length;

  let combinedSources = Array(numSources).fill(null);
  docSources.forEach((source, i) => {
    if (source) {
      combinedSources[source.context_index! - 1] = source;
    }
  });

  qaSources.forEach((source, i) => {
    if (source) {
      combinedSources[source.context_index! - 1] = source;
    }
  });

  let usedSources: any[] = [];

  // Reorder citations so they're sequential for the end user
  const removeCitations = false;
  let processedAnswer =
    processAnswerCitations(originalAnswer, removeCitations) || '';

  const { usedCitationIndexes } = reorderCitations(processedAnswer);

  // Reposition "combinedSources" based on the reordered citations
  combinedSources.forEach((origSource, origSourceIdx) => {
    if (usedCitationIndexes.includes(origSourceIdx + 1)) {
      usedSources.push(origSource);
    }
  });

  let processedAnswerWithCitations = processedAnswer;

  usedCitationIndexes.forEach((citationNumber, idx) => {
    const source = usedSources[idx];
    const sourcePageMetadata = source.metadata.page_numbers || [];
    let sourcePageReference = '';
    if (sourcePageMetadata.length > 0) {
      sourcePageReference = `, p. ${sourcePageMetadata.join(', ')}`;
    }

    if (
      source.document_label === 'Tribble Knowledge Base' ||
      source.document_label === 'Community Notes'
    ) {
      // Special -- don't include the "learned" stuff
      processedAnswerWithCitations = processedAnswerWithCitations.replaceAll(
        `[${citationNumber}]`,
        '',
      );
    } else {
      processedAnswerWithCitations = processedAnswerWithCitations.replaceAll(
        `[${citationNumber}]`,
        `[${source.document_label}${sourcePageReference}]`,
      );
    }
  });

  processedAnswerWithCitations = processedAnswerWithCitations
    .replaceAll('][', '; ')
    .replace(/\s{2,}/g, ' ');

  return processedAnswerWithCitations;
};

export const getGeneratedAnswer = (
  question: QuestionDto,
  inlineCitations: boolean = false,
): string => {
  const multiVersionAnswers =
    question.output_original?.answer_versions !== undefined;

  return multiVersionAnswers
    ? getMultiVersionAnswers(question).join('\n\n').trim()
    : inlineCitations
      ? getAnswerWithInlineCitations(question, inlineCitations)
      : question.output_original!.answer || '';
};

const getAnswerConfidence = (question: QuestionDto): string => {
  const getConfidenceLabel = (
    confidenceScore: number,
    couldNotAnswer: boolean,
  ) => {
    return couldNotAnswer
      ? 'Could not answer'
      : [
          'Low Confidence',
          'Low Confidence',
          'Medium Confidence',
          'High Confidence',
        ][confidenceScore];
  };

  if (question.output_original?.answer_versions !== undefined) {
    return question.output_original?.answer_versions
      .reduce((agg, answer, idx) => {
        let confidenceScore = answer.confidence_score ?? -1;
        confidenceScore =
          confidenceScore > 3 ? 3 : confidenceScore < 0 ? 0 : confidenceScore;
        const confidenceLabel = getConfidenceLabel(
          confidenceScore,
          answer.review_message?.includes(
            'Could not generate an answer to the question',
          ) ?? false,
        );

        return agg + `Answer ${idx + 1}: ${confidenceLabel}\n\n`;
      }, '')
      .trim();
  } else {
    let confidenceScore = question.output_original?.confidence_score ?? -1;
    confidenceScore =
      confidenceScore > 3 ? 3 : confidenceScore < 0 ? 0 : confidenceScore;
    return getConfidenceLabel(
      confidenceScore,
      couldNotGenerateAnswer(question) ?? false,
    );
  }
};

export const couldNotGenerateAnswer = (question: QuestionDto) => {
  if (isMultiVersionAnswer(question)) {
    return question.output_original?.answer_versions?.every((answer) => {
      // Answer RFP uses this same string...need some shared constants...
      return answer?.review_message?.includes(
        'Could not generate an answer to the question',
      );
    });
  } else {
    return question.output_original?.review_message?.includes(
      'Could not generate an answer to the question',
    );
  }
};

export const isMultiVersionAnswer = (question: QuestionDto) => {
  return (
    question.output_original?.answer_versions !== undefined &&
    question.output_original?.answer_versions?.length > 0
  );
};

export const getSourceCitationByIndex = (
  question: QuestionDto,
  citationNumber: number,
  multiAnswerIndex: number = -1,
): ContentSourceDoc | ContentSourceQA | null => {
  const answer =
    multiAnswerIndex === -1
      ? question.output_original
      : question.output_original?.answer_versions?.[multiAnswerIndex];
  const docSources = answer?.documents?.docs || [];
  const qaSources = answer?.documents?.qa || [];

  let combinedSources = Array(docSources.length + qaSources.length).fill(null);
  docSources.forEach((source, i) => {
    combinedSources[source.context_index! - 1] = source;
  });

  qaSources.forEach((source, i) => {
    combinedSources[source.context_index! - 1] = source;
  });

  if (citationNumber > combinedSources.length) {
    return null;
  }

  return combinedSources[citationNumber - 1];
};

// Answer Entries need to store their own version of content_detail.output_original.documents
export const getSourceCitationForAnswerEntryByIndex = (
  answerEntry: MappedAnswerEntry,
  citationNumber: number,
): ContentSourceDoc | ContentSourceQA | null => {
  const docSources = answerEntry?.details?.documents?.docs || [];
  const qaSources = answerEntry?.details?.documents?.qa || [];

  let combinedSources = Array(docSources.length + qaSources.length).fill(null);
  docSources.forEach((source, i) => {
    combinedSources[source.context_index! - 1] = source;
  });

  qaSources.forEach((source, i) => {
    combinedSources[source.context_index! - 1] = source;
  });

  if (citationNumber > combinedSources.length) {
    return null;
  }

  return combinedSources[citationNumber - 1];
};

export const markdownToHtml = (markdown: string) => {
  if (!markdown) return '';

  return markdown
    .replaceAll(/\\n/g, '<br>')
    .replaceAll(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replaceAll(/\\/g, '');
};

export const convertTimestampToDate = (ts: number): Date | undefined => {
  const EPOCH_NO_MILLI = 1700000000;
  const EPOCH_W_MILLI = EPOCH_NO_MILLI * 1000;

  // Make sure it looks like a valid timestamp
  if (!isNaN(Number(ts)) && Number(ts) > EPOCH_NO_MILLI) {
    const ts_valued = Number(ts);
    if (ts_valued > EPOCH_NO_MILLI && ts_valued < EPOCH_W_MILLI) {
      return new Date(Number(ts) * 1000);
    } else {
      return new Date(Number(ts));
    }
  } else {
    return undefined;
  }
};

export const getQuestionnaireCreationDate = (
  questionnaire: QuestionnaireDto | Rfp,
) => {
  let createdDate;
  if (questionnaire.created_date) {
    createdDate = new Date(questionnaire.created_date);
  } else if (questionnaire.details?.agentParams?.thread) {
    // If can't find created_date, then see if we can find via the agentParams timestamp
    const thread_ts = String(questionnaire.details.agentParams.thread).split(
      '.',
    )[0];
    createdDate = convertTimestampToDate(Number(thread_ts));
  }
  return createdDate;
};

export const projectHasError = (projectContent: ProjectContent) => {
  return (
    projectContent.details?.agentParams?.error?.length > 0 ||
    projectContent.status?.toLocaleLowerCase()?.startsWith('error')
  );
};

// If an E2E Project has been in the analyzing state for more than 2 days, it's probably an error
export const isE2EInErrorState = (projectContent: ProjectContent) => {
  return projectHasError(projectContent);
};

export const isPossibleError = (projectContent: ProjectContent) => {
  const lastUpdated = projectContent.lastUpdated ?? projectContent.createdDate;
  const isAnalyzing = projectContent.statusLabel
    ?.toLowerCase()
    ?.startsWith('analyzing');
  const isGenerating = projectContent.statusLabel
    ?.toLowerCase()
    ?.startsWith('generating');
  const MILLIS_TWO_DAYS = 2 * 24 * 60 * 60 * 1000;

  if (lastUpdated !== undefined && lastUpdated != null) {
    const today = new Date();
    if (lastUpdated < today) {
      // If it's been more than 2 days since the RFP was created, it's probably an error
      if (
        (isAnalyzing || isGenerating) &&
        today.getTime() - lastUpdated.getTime() > MILLIS_TWO_DAYS
      ) {
        return true;
      }
    }
  }
};

export const isModifiedAnswer = (question: QuestionDto | undefined) => {
  if (!question) {
    return false;
  }

  return (
    question.state == 'modified' ||
    (question?.output_modified?.text !== undefined &&
      question?.output_modified?.text !== null)
  );
};

/*
In general, should use the translated answer if the key is present.
However, corner-case:

Case 1
- original answer
- original gets translated
- user modifies the translated
Result: should show modified.

Case 2
- original answer
- user modifies the original
- user then translates (will use modified answer)
Result: should show translated

Also need to handle the revert workflow for each (as appropriate).

Note: this is why we store the "source_text" in the translation object.
*/
export const shouldUseTranslatedAnswer = (
  question: QuestionDto | undefined,
) => {
  if (!question) {
    return false;
  }

  const translatedText = question.output_original?.translation?.translation;
  const translatedSourceText =
    question.output_original?.translation?.source_text;

  const isModified = isModifiedAnswer(question);

  // If not modified, easy: just use translated text if it's available
  if (!translatedText) {
    return false;
  } else if (!isModified) {
    return true;
  } else {
    // Handle cases commented above
    if (translatedSourceText == question?.output_modified?.text) {
      return true;
    } else {
      return false;
    }
  }
};

// Taken from Holodec: src/popup/pages/HomePage/pages/ViewQuestionnaire/util.ts
export const getInProgressJobs = (job_log_data) => {
  if (!job_log_data) {
    return [];
  }

  // Look in the job_log_data field for any records where:
  // state = 'processing' AND duration.start >= 6 hours ago
  // (do the start comparison as chance a job failed and didn't clean up)
  return job_log_data?.filter((log) => {
    const sixHoursAgo = new Date();
    sixHoursAgo.setHours(sixHoursAgo.getHours() - 24);

    // start was written by Python time.time() which is in seconds
    // (but just check anyway to be safe)
    const multiplier = Number(log.duration.start) > 10000000000 ? 1 : 1000;
    const jobLogStartTs = Number(log.duration.start) * multiplier;

    return log.state === 'processing' && jobLogStartTs >= sixHoursAgo.getTime();
  });
};

export const openChatSession = async ({
  question,
  answerEntry,
  questionGrouping,
  metadataFilterValues,
  projectContent,
  customerName,
  token,
  rootStore,
  onNewChatSession,
  enableReplaceCurrentAnswer = true,
  seedUserMessage,
}: {
  question?: QuestionDto;
  answerEntry?: MappedAnswerEntry;
  questionGrouping: Grouping | undefined;
  metadataFilterValues: MetadataFilterValue[];
  projectContent: ProjectContent;
  customerName: string;
  token: string;
  rootStore: RootStore;
  onNewChatSession: (conversationId: number) => void;
  enableReplaceCurrentAnswer?: boolean;
  seedUserMessage?: string;
}) => {
  if (!question && !answerEntry) {
    return;
  }

  // Check if MDF at project level, or question grouping
  let mdfIds: number[] = [];
  if (questionGrouping?.grouping_metadata_filter) {
    const gmdf = questionGrouping.grouping_metadata_filter;

    if (gmdf?.multiple_types) {
      mdfIds = gmdf.multiple_types.reduce((acc, mdft) => {
        const mdfIds = (mdft.values ?? []).map((value) => {
          const mdf = metadataFilterValues?.find(
            (mdf) =>
              mdf.value === value && mdf.type_id === Number(mdft.type_id),
          );
          return mdf?.id;
        }) as number[];
        acc.push(...mdfIds);
        return acc;
      }, [] as number[]);

      // There is a special type_id === 0 grouping MDF
    } else if (!isNaN(gmdf['type_id']) && gmdf['type_id'] > 0) {
      mdfIds = (gmdf?.values ?? []).map((value) => {
        const mdf = metadataFilterValues?.find(
          (mdf) => mdf.value === value && mdf.type_id === Number(gmdf?.type_id),
        );
        return mdf?.id;
      }) as number[];
    }
  } else if (projectContent.metadataFilter) {
    // A bit repetitive of above, but hopefully easier to read
    if (projectContent.metadataFilter?.multiple_types) {
      mdfIds = projectContent.metadataFilter.multiple_types.reduce(
        (acc, mdft) => {
          const mdfIds = (mdft.values ?? []).map((value) => {
            const mdf = metadataFilterValues?.find(
              (mdf) =>
                mdf.value === value && mdf.type_id === Number(mdft.type_id),
            );
            return mdf?.id;
          }) as number[];
          acc.push(...mdfIds);
          return acc;
        },
        [] as number[],
      );
    } else {
      mdfIds = (projectContent.metadataFilter?.values ?? []).map((value) => {
        const mdf = metadataFilterValues?.find(
          (mdf) =>
            mdf.value === value &&
            mdf.type_id === Number(projectContent.metadataFilter?.type_id),
        );
        return mdf?.id;
      }) as number[];
    }
  }

  // If conversationId doesn't exist, create one:
  // Seed with the contexts from the content_detail,
  // And create an initial user message / assistant response.

  // TODO: handle modified answer scenario
  let conversationId = answerEntry?.conversationId ?? question?.conversation_id;

  let createConversation;
  if (answerEntry && !answerEntry.conversationId) {
    createConversation = await createNewConversationForAnswerEntry(
      token,
      answerEntry!.id,
      answerEntry!.contentDetailId,
      customerName,
      'WEBCHAT',
      mdfIds,
    );
  } else if (question && !question.conversation_id) {
    createConversation = await createNewConversationForQuestion(
      token,
      question!.id!,
      customerName,
      'WEBCHAT',
      mdfIds,
    );
  }

  if (createConversation) {
    if (createConversation?.response) {
      conversationId = createConversation.response;
      rootStore.openChatSession({
        conversationId,
        contentDetailId: question?.id,
        answerEntryId: answerEntry?.id,
        contentId: projectContent.id,
        enableReplaceCurrentAnswer,
        seedUserMessage,
      });

      if (conversationId && onNewChatSession) {
        onNewChatSession(conversationId!);
      }
    } else {
      rootStore.setNotification({
        message: 'Failed to create chat session',
        type: 'error',
      });
    }
  } else {
    rootStore.openChatSession({
      conversationId,
      contentDetailId: question?.id,
      answerEntryId: answerEntry?.id,
      contentId: projectContent.id,
      enableReplaceCurrentAnswer,
      seedUserMessage,
    });
  }
};

export const getSourceLabel = (source: ContentSourceDoc | ContentSourceQA) => {
  let sourceLabel = source?.document_label ?? source?.file_name ?? '';

  const isSlack = source?.document_type?.toLowerCase() === 'slack';
  if (isSlack) {
    sourceLabel = `#${sourceLabel}`;
  }

  return sourceLabel;
};

export const getSourceDeeplink = (
  source: ContentSourceDoc | ContentSourceQA,
) => {
  const hasUrl = source?.file_name?.startsWith('https://');
  if (hasUrl) {
    return source.file_name;
  }

  const hostUrl = new URL(window.location.href);
  let deeplink = `${hostUrl.origin}/sources/source/${source.document_id}`;
  if (
    'page_numbers' in source.metadata &&
    Array.isArray(source.metadata.page_numbers) &&
    source.metadata.page_numbers.length > 0
  ) {
    deeplink += `?page=${source.metadata.page_numbers[0]}`;
  } else if ('row_index' in source.metadata) {
    deeplink += `/fact/${source.embedding_id}`;
  } else if (
    'slack_link' in source.metadata &&
    source.metadata.slack_link != ''
  ) {
    deeplink = String(source.metadata.slack_link);
  }

  return deeplink;
};

export const cleanUpMarkdownForFilledInExcel = (answer: string) => {
  // Replace '**' with ''.
  // Add more as needed
  return answer.replace(/\*\*/g, '');
};
