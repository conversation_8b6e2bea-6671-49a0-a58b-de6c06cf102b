cells:
  - kind: 2
    languageId: sql
    value: >-
      -- Create a temporary function to collect all Gong documents across
      schemas

      CREATE OR REPLACE FUNCTION get_all_gong_documents()

      RETURNS TABLE (
          schema_name text,
          document_id bigint,
          file_name text,
          created_date date,
          status text,
          use_for_generation boolean,
          external_source text,
          external_source_id text,
          has_embeddings boolean
      ) AS $$

      DECLARE 
          schema_record record;
          query_text text;
      BEGIN
          FOR schema_record IN (
              SELECT nspname as schema_name 
              FROM pg_namespace 
              WHERE nspname ~ '^c[0-9]+$'
              AND nspname NOT LIKE '%_custom'
              AND nspname NOT LIKE '%_integration'
              ORDER BY nspname
          )
          LOOP
              -- Check if document table exists
              IF EXISTS (
                  SELECT 1 FROM information_schema.tables 
                  WHERE table_schema = schema_record.schema_name AND table_name = 'document'
              ) THEN
                  query_text := format('
                      SELECT 
                          %L::text as schema_name,
                          d.id as document_id,
                          d.file_name,
                          d.created_date,
                          d.status,
                          d.use_for_generation,
                          d.metadata_json->>''externalDocSource'' as external_source,
                          d.metadata_json->>''externalDocSourceId'' as external_source_id,
                          CASE 
                              WHEN EXISTS (
                                  SELECT 1 FROM information_schema.tables 
                                  WHERE table_schema = %L AND table_name = ''embedding''
                              ) THEN EXISTS (
                                  SELECT 1 FROM %I.embedding e WHERE e.document_id = d.id
                              )
                              ELSE NULL
                          END as has_embeddings
                      FROM %I.document d
                      WHERE d.deleted_date IS NULL 
                      AND LOWER(d.metadata_json->>''externalDocSource'') = ''gong''
                      ORDER BY d.created_date DESC',
                      schema_record.schema_name, 
                      schema_record.schema_name,
                      schema_record.schema_name,
                      schema_record.schema_name
                  );
                  
                  RETURN QUERY EXECUTE query_text;
              END IF;
          END LOOP;
      END;

      $$ LANGUAGE plpgsql;


      -- Execute the function to get all results

      SELECT * FROM get_all_gong_documents()

      ORDER BY schema_name, created_date DESC;
    metadata: {}
  - kind: 2
    languageId: sql
    value: >-
      -- Create a function to get schema summary statistics

      CREATE OR REPLACE FUNCTION get_schema_summary()

      RETURNS TABLE (
          schema_name text,
          has_document_table boolean,
          total_documents bigint,
          gong_documents bigint,
          gong_percentage numeric
      ) AS $$

      DECLARE 
          schema_record record;
          doc_count bigint;
          gong_count bigint;
      BEGIN
          FOR schema_record IN (
              SELECT nspname as schema_name 
              FROM pg_namespace 
              WHERE nspname ~ '^c[0-9]+$'
              AND nspname NOT LIKE '%_custom'
              AND nspname NOT LIKE '%_integration'
              ORDER BY nspname
          )
          LOOP
              -- Check if document table exists
              IF EXISTS (
                  SELECT 1 FROM information_schema.tables 
                  WHERE table_schema = schema_record.schema_name AND table_name = 'document'
              ) THEN
                  -- Get document counts
                  EXECUTE format('SELECT COUNT(*) FROM %I.document WHERE deleted_date IS NULL', 
                                schema_record.schema_name) INTO doc_count;
                  EXECUTE format('SELECT COUNT(*) FROM %I.document WHERE deleted_date IS NULL AND LOWER(metadata_json->>''externalDocSource'') = ''gong''', 
                                schema_record.schema_name) INTO gong_count;
                  
                  -- Return row for this schema
                  RETURN QUERY SELECT 
                      schema_record.schema_name::text,
                      true::boolean,
                      doc_count,
                      gong_count,
                      CASE 
                          WHEN doc_count > 0 THEN ROUND((gong_count::numeric / doc_count::numeric) * 100, 2)
                          ELSE 0::numeric
                      END;
              ELSE
                  -- Schema has no document table
                  RETURN QUERY SELECT 
                      schema_record.schema_name::text,
                      false::boolean,
                      0::bigint,
                      0::bigint,
                      0::numeric;
              END IF;
          END LOOP;
      END;

      $$ LANGUAGE plpgsql;


      -- Get summary with totals

      WITH schema_summary AS (
          SELECT * FROM get_schema_summary()
      ),

      totals AS (
          SELECT 
              'TOTALS' as schema_name,
              NULL::boolean as has_document_table,
              SUM(total_documents) as total_documents,
              SUM(gong_documents) as gong_documents,
              CASE 
                  WHEN SUM(total_documents) > 0 THEN ROUND((SUM(gong_documents)::numeric / SUM(total_documents)::numeric) * 100, 2)
                  ELSE 0::numeric
              END as gong_percentage
          FROM schema_summary
          WHERE has_document_table = true
      )

      SELECT * FROM schema_summary

      WHERE total_documents > 0 OR has_document_table = false

      UNION ALL

      SELECT * FROM totals

      ORDER BY 
          CASE WHEN schema_name = 'TOTALS' THEN 1 ELSE 0 END,
          gong_documents DESC, 
          total_documents DESC;
    metadata: {}
  - kind: 2
    languageId: sql
    value: |-
      -- Soft delete Gong documents that have no embeddings
      -- These are likely failed processing attempts or duplicates
      UPDATE {schema}.document 
      SET status = 'deleted', 
          use_for_generation = false,
          deleted_date = CURRENT_TIMESTAMP,
          deleted_by_id = (
              SELECT u.id 
              FROM tribble.user u 
              WHERE u.email = '<EMAIL>' 
              LIMIT 1
          )
      WHERE id IN (
          SELECT d.id
          FROM {schema}.document d
          JOIN {schema}.document_type dt ON d.document_type_id = dt.id
          WHERE d.status != 'deleted'
              AND d.use_for_generation = true
              AND dt.name = 'Call Transcript'
              AND d.metadata_json->>'externalDocSource' = 'gong'
              AND NOT EXISTS (
                  SELECT 1 
                  FROM {schema}.embedding e 
                  WHERE e.document_id = d.id
              )
      );
    metadata: {}
  - kind: 2
    languageId: sql
    value: |
      -- Restore Gong documents that were soft deleted by this migration
      UPDATE {schema}.document 
      SET status = 'active', 
          use_for_generation = true,
          deleted_date = NULL,
          deleted_by_id = NULL
      WHERE id IN (
          SELECT d.id
          FROM {schema}.document d
          JOIN {schema}.document_type dt ON d.document_type_id = dt.id
          WHERE d.status = 'deleted'
              AND dt.name = 'Call Transcript'
              AND d.metadata_json->>'externalDocSource' = 'gong'
              AND NOT EXISTS (
                  SELECT 1 
                  FROM {schema}.embedding e 
                  WHERE e.document_id = d.id
              )
      );
    metadata: {}
metadata:
  conn:
    id: ACHw43KqtTQZZ5S03Guiu
    name: Local
  database: postgres
  schema: public
