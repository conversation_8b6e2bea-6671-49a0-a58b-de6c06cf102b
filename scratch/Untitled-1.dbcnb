-- Soft delete Gong documents that have no embeddings
-- These are likely failed processing attempts or duplicates
UPDATE {schema}.document 
SET status = 'deleted', 
    use_for_generation = false,
    deleted_date = CURRENT_TIMESTAMP,
    deleted_by_id = (
        SELECT u.id 
        FROM tribble.user u 
        WHERE u.email = '<EMAIL>' 
        LIMIT 1
    )
WHERE id IN (
    SELECT d.id
    FROM {schema}.document d
    JOIN {schema}.document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM {schema}.embedding e 
            WHERE e.document_id = d.id
        )
);

-- Restore Gong documents that were soft deleted by this migration
UPDATE {schema}.document 
SET status = 'active', 
    use_for_generation = true,
    deleted_date = NULL,
    deleted_by_id = NULL
WHERE id IN (
    SELECT d.id
    FROM {schema}.document d
    JOIN {schema}.document_type dt ON d.document_type_id = dt.id
    WHERE d.status = 'deleted'
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM {schema}.embedding e 
            WHERE e.document_id = d.id
        )
);
