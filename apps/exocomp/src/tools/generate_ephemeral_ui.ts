/*
 * Generate Ephemeral UI Tool
 *
 * This tool generates dynamic UI forms for complex data collection tasks
 * that would be cumbersome through conversation alone. The UI is ephemeral,
 * meaning it exists only for the duration of the task and then dissolves.
 *
 * Changes made: Created new tool for ephemeral UI generation
 */

import {
  ActionsBlock,
  Checkboxes,
  InputBlock,
  KnownBlock,
  PlainTextOption,
  RadioButtons,
  StaticSelect,
} from '@slack/bolt';
import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { HubSpotClient } from '@tribble/hubspot';
import { Redis, constants as expiries } from '@tribble/redis';
import { ClientSchema } from '@tribble/tribble-db';
import {
  ConversationDetailId,
  ConversationId,
} from '@tribble/tribble-db/types';
import { Connection as SalesforceConnection } from 'jsforce';
import { v4 as uuidv4 } from 'uuid';
import { ConversationMessage } from '../conversations/message.ts';
import { clampText } from '../views/clamp_text.ts';
import { ToolCall } from './tool_call.ts';

// Types for UI generation
export interface UIField {
  type: 'text' | 'select' | 'checkbox' | 'radio' | 'textarea';
  name: string;
  label: string;
  placeholder?: string;
  options?: { value: string; label: string }[];
  required?: boolean;
  defaultValue?: string | string[];
  helpText?: string;
}

export interface UISection {
  title?: string;
  fields: UIField[];
}

export interface GenerateUIArgs {
  ui_purpose: string;
  title: string;
  description?: string;
  sections: UISection[];
  submit_label?: string;
  cancel_label?: string;
  prefill_from_context?: boolean;
}

export interface EphemeralUIState {
  ui_id: string;
  schema: ClientSchema;
  conversation_id: ConversationId;
  conversation_detail_id: ConversationDetailId;
  purpose: string;
  fields: UIField[];
  created_at: number;
  expires_at: number;
}

export class GenerateEphemeralUI extends ToolCall {
  static tool_name = 'generate_ephemeral_ui';

  private uiArgs: GenerateUIArgs;
  private schema: ClientSchema;
  private conversationId: ConversationId;
  private conversationDetailId: ConversationDetailId;
  private messages: ConversationMessage[];

  constructor(
    config: {
      schema: ClientSchema;
      conversationId: ConversationId;
      conversationDetailId: ConversationDetailId;
      messages: ConversationMessage[];
      hubspotClient?: HubSpotClient;
      salesforceConn?: SalesforceConnection;
      logger?: Logger;
    },
    args: GenerateUIArgs,
  ) {
    super(config.logger);
    this.uiArgs = args;
    this.schema = config.schema;
    this.conversationId = config.conversationId;
    this.conversationDetailId = config.conversationDetailId;
    this.messages = config.messages;
  }

  static getPromptDescription(_: any): string {
    return (
      `You have access to a tool that generates ephemeral UI forms for complex data collection. ` +
      `Use this when gathering multiple parameters would be cumbersome through conversation. ` +
      `The UI will appear in Slack as an interactive form that users can fill out. ` +
      `Consider using this tool when users mention: ` +
      `- Searching with "multiple criteria" or "multiple filters" ` +
      `- Migration or sync between systems you have write access to.` +
      `- Bulk operations or updates ` +
      `- Configuration or setup tasks ` +
      `- Any task requiring 3+ related inputs. ` +
      `If you have enough context about the system/data source, generate a form immediately rather than asking clarifying questions.`
    );
  }

  static getJsonSchema(_: any): Tool {
    return {
      type: 'function',
      function: {
        name: GenerateEphemeralUI.tool_name,
        description:
          'Generate an ephemeral UI form for structured data collection. Use this for multi-criteria searches, complex configurations, bulk operations, or any task requiring multiple related inputs.',
        parameters: {
          type: 'object',
          properties: {
            ui_purpose: {
              type: 'string',
              description:
                'The purpose of this UI (e.g., "deal_search", "data_migration_config", "bulk_update_params")',
            },
            title: {
              type: 'string',
              description: 'The title of the form',
            },
            description: {
              type: 'string',
              description:
                'Optional description to show at the top of the form',
            },
            sections: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  title: {
                    type: 'string',
                    description: 'Section title',
                  },
                  fields: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        type: {
                          type: 'string',
                          enum: [
                            'text',
                            'select',
                            'checkbox',
                            'radio',
                            'textarea',
                          ],
                          description: 'Field type',
                        },
                        name: {
                          type: 'string',
                          description: 'Field identifier',
                        },
                        label: {
                          type: 'string',
                          description: 'Field label',
                        },
                        placeholder: {
                          type: 'string',
                          description: 'Placeholder text',
                        },
                        options: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              value: { type: 'string' },
                              label: { type: 'string' },
                            },
                            required: ['value', 'label'],
                          },
                          description:
                            'Options for select/checkbox/radio fields',
                        },
                        required: {
                          type: 'boolean',
                          description: 'Whether field is required',
                        },
                        defaultValue: {
                          type: 'string',
                          description: 'Default value',
                        },
                        helpText: {
                          type: 'string',
                          description: 'Help text to display',
                        },
                      },
                      required: ['type', 'name', 'label'],
                    },
                  },
                },
                required: ['fields'],
              },
            },
            submit_label: {
              type: 'string',
              description: 'Label for submit button (default: "Submit")',
            },
            cancel_label: {
              type: 'string',
              description: 'Label for cancel button (default: "Cancel")',
            },
            prefill_from_context: {
              type: 'boolean',
              description:
                'Whether to prefill fields from conversation context',
            },
          },
          required: ['ui_purpose', 'title', 'sections'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return '🎨 Generating interactive form...';
  }

  getCompletionMessage(): string {
    return '✅ Interactive form ready';
  }

  async performCall(): Promise<string> {
    // Generate unique UI ID
    const uiId = `ui_${uuidv4()}`;

    // Store UI state in Redis for later retrieval
    const uiState: EphemeralUIState = {
      ui_id: uiId,
      schema: this.schema,
      conversation_id: this.conversationId,
      conversation_detail_id: this.conversationDetailId,
      purpose: this.uiArgs.ui_purpose,
      fields: this.uiArgs.sections.flatMap((s) => s.fields),
      created_at: Date.now(),
      expires_at: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
    };

    const redis = new Redis();
    const cacheKey = `ephemeral_ui:${uiId}`;
    await redis.set(cacheKey, JSON.stringify(uiState), expiries.EX_1_DAY);

    // Prefill values from context if requested
    if (this.uiArgs.prefill_from_context) {
      await this.prefillFromContext();
    }

    return JSON.stringify({
      ui_id: uiId,
      purpose: this.uiArgs.ui_purpose,
      message: `Generated ephemeral UI form for ${this.uiArgs.ui_purpose}`,
    });
  }

  getCompletionBlocks(args: any): KnownBlock[] {
    const blocks: KnownBlock[] = [];
    let ui_id: string;
    const { content } = args;
    if (content && typeof content === 'string') {
      try {
        ui_id = JSON.parse(content).ui_id;
      } catch (error) {
        console.error('Error parsing content:', error);
      }
    }

    if (!ui_id) {
      return [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Error: Could not generate UI. Please try again.',
          },
        },
      ];
    }

    // Header section
    blocks.push({
      type: 'header',
      text: {
        type: 'plain_text',
        text: clampText(this.uiArgs.title || ' ', 150),
      },
    });

    // Description if provided
    if (this.uiArgs.description) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(this.uiArgs.description || ' ', 3000),
        },
      });
    }

    // Generate blocks for each section
    this.uiArgs.sections.forEach((section, sectionIdx) => {
      // Section header
      if (section.title) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: clampText(`*${section.title || ' '}*`, 3000),
          },
        });
      }

      // Generate blocks for each field
      section.fields.forEach((field, fieldIdx) => {
        const blockId = clampText(`${ui_id}_${sectionIdx}_${fieldIdx}`, 255);

        switch (field.type) {
          case 'text':
          case 'textarea':
            blocks.push(this.createInputBlock(field, blockId));
            break;
          case 'select':
            blocks.push(this.createSelectBlock(field, blockId));
            break;
          case 'checkbox':
            blocks.push(this.createCheckboxBlock(field, blockId));
            break;
          case 'radio':
            blocks.push(this.createRadioBlock(field, blockId));
            break;
        }
      });

      // Add divider between sections
      if (sectionIdx < this.uiArgs.sections.length - 1) {
        blocks.push({ type: 'divider' });
      }
    });

    // Action buttons
    const actionBlock: ActionsBlock = {
      type: 'actions',
      elements: [
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: clampText(this.uiArgs.submit_label || 'Submit', 75),
          },
          style: 'primary',
          action_id: 'ephemeral_ui_submit',
          value: JSON.stringify({
            ui_id,
            schema: this.schema,
            conversation_id: this.conversationId,
            conversation_detail_id: this.conversationDetailId,
          }),
        },
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: clampText(this.uiArgs.cancel_label || 'Cancel', 75),
          },
          action_id: 'ephemeral_ui_cancel',
          value: JSON.stringify({ ui_id }),
        },
      ],
    };
    blocks.push(actionBlock);

    return blocks;
  }

  private createInputBlock(field: UIField, blockId: string): InputBlock {
    return {
      type: 'input',
      block_id: blockId,
      label: {
        type: 'plain_text',
        text: field.label,
      },
      element: {
        type: 'plain_text_input',
        action_id: clampText(field.name, 255),
        max_length: 3000,
        ...(field.placeholder
          ? {
              placeholder: {
                type: 'plain_text',
                text: clampText(field.placeholder || ' ', 150),
              },
            }
          : {}),
        initial_value: field.defaultValue as string,
        multiline: field.type === 'textarea',
      },
      optional: !field.required,
      hint: field.helpText
        ? {
            type: 'plain_text',
            text: field.helpText,
          }
        : undefined,
    };
  }

  private createSelectBlock(field: UIField, blockId: string): InputBlock {
    const selectElement: StaticSelect = {
      type: 'static_select',
      action_id: field.name,
      placeholder: {
        type: 'plain_text',
        text: clampText(field.placeholder || 'Select an option', 150),
      },
    };

    if (field.options && field.options.length > 0) {
      // Validate and filter out any options with empty values (Slack doesn't allow them)
      const validOptions = field.options
        .filter((opt) => opt.value && opt.value.trim() !== '')
        .slice(0, 100); // Limit to 100 options

      if (validOptions.length === 0) {
        // If all options were invalid, add a default option
        validOptions.push({ value: 'none', label: 'No valid options' });
      }

      selectElement.options = validOptions.map(
        (opt) =>
          ({
            text: {
              type: 'plain_text',
              text: clampText(opt.label || opt.value, 75),
            },
            value: clampText(opt.value, 150),
          }) as PlainTextOption,
      );

      if (field.defaultValue && typeof field.defaultValue === 'string') {
        // Ensure default value is not empty
        const defaultValue = field.defaultValue.trim() || 'none';
        const defaultOpt = validOptions.find(
          (opt) => opt.value === defaultValue,
        );
        if (defaultOpt) {
          selectElement.initial_option = {
            text: {
              type: 'plain_text',
              text: clampText(defaultOpt.label || ' ', 75),
            },
            value: clampText(defaultOpt.value || ' ', 150),
          } as PlainTextOption;
        }
      }
    }

    return {
      type: 'input',
      block_id: blockId,
      label: {
        type: 'plain_text',
        text: clampText(field.label || ' ', 75),
      },
      element: selectElement,
      optional: !field.required,
      hint: field.helpText
        ? {
            type: 'plain_text',
            text: clampText(field.helpText || ' ', 75),
          }
        : undefined,
    };
  }

  private createCheckboxBlock(field: UIField, blockId: string): InputBlock {
    // Validate and filter out any options with empty values (Slack doesn't allow them)
    const validOptions = (field.options || []).filter(
      (opt) => opt.value && opt.value.trim() !== '',
    );

    if (validOptions.length === 0) {
      // If no valid options, add a default option
      validOptions.push({ value: 'none', label: 'No options available' });
    }

    const options = validOptions.map((opt) => ({
      text: {
        type: 'plain_text',
        text: clampText(opt.label || ' ', 75),
      },
      value: clampText(opt.value, 150),
    })) as PlainTextOption[];

    const checkboxElement: Checkboxes = {
      type: 'checkboxes',
      action_id: clampText(`ephem_UI_${field.name}`, 255),
      options,
    };

    if (field.defaultValue && Array.isArray(field.defaultValue)) {
      // Filter default values to ensure they're not empty and exist in valid options
      const validDefaultValues = field.defaultValue
        .filter((v) => v && v.trim() !== '')
        .filter((v) => validOptions.some((opt) => opt.value === v));

      if (validDefaultValues.length > 0) {
        checkboxElement.initial_options = validDefaultValues.map((val) => {
          const opt = validOptions.find((o) => o.value === val)!;
          return {
            text: {
              type: 'plain_text',
              text: clampText(opt.label, 75),
            },
            value: clampText(opt.value, 150),
          };
        });
      }
    }

    return {
      type: 'input',
      block_id: blockId,
      label: {
        type: 'plain_text',
        text: clampText(field.label, 75),
      },
      dispatch_action: false,
      element: checkboxElement,
      optional: !field.required,
      hint: field.helpText
        ? {
            type: 'plain_text',
            text: clampText(field.helpText || ' ', 75),
          }
        : undefined,
    };
  }

  private createRadioBlock(field: UIField, blockId: string): InputBlock {
    // Validate and filter out any options with empty values (Slack doesn't allow them)
    const validOptions = (field.options || []).filter(
      (opt) => opt.value && opt.value.trim() !== '',
    );

    if (validOptions.length === 0) {
      // If no valid options, add a default option
      validOptions.push({ value: 'none', label: 'No options available' });
    }

    const options = validOptions.map((opt) => ({
      text: {
        type: 'plain_text',
        text: clampText(opt.label || ' ', 75),
      },
      value: clampText(opt.value, 150),
    })) as PlainTextOption[];

    const radioElement: RadioButtons = {
      type: 'radio_buttons',
      action_id: clampText(`ephem_UI_${field.name}`, 255),
      options,
    };

    if (field.defaultValue && typeof field.defaultValue === 'string') {
      // Ensure default value is not empty and exists in valid options
      const defaultValue = field.defaultValue.trim() || 'none';
      const defaultOpt = validOptions.find((opt) => opt.value === defaultValue);
      if (defaultOpt) {
        radioElement.initial_option = {
          text: {
            type: 'plain_text',
            text: clampText(defaultOpt.label, 75),
          },
          value: clampText(defaultOpt.value, 150),
        };
      }
    }

    return {
      type: 'input',
      block_id: blockId,
      label: {
        type: 'plain_text',
        text: clampText(field.label, 75),
      },
      element: radioElement,
      optional: !field.required,
      hint: field.helpText
        ? {
            type: 'plain_text',
            text: clampText(field.helpText || ' ', 75),
          }
        : undefined,
    };
  }

  private async prefillFromContext(): Promise<void> {
    // Analyze conversation history to extract potential default values
    // This is a simplified implementation - could be enhanced with LLM analysis

    const recentMessages = this.messages.slice(-10); // Last 10 messages

    // Look for mentions of specific systems or parameters
    for (const section of this.uiArgs.sections) {
      for (const field of section.fields) {
        // Simple pattern matching for common scenarios
        if (
          field.name.toLowerCase().includes('source') ||
          field.name.toLowerCase().includes('from')
        ) {
          for (const msg of recentMessages) {
            const content = typeof msg.content === 'string' ? msg.content : '';
            if (content.toLowerCase().includes('hubspot')) {
              field.defaultValue = 'hubspot';
            } else if (content.toLowerCase().includes('salesforce')) {
              field.defaultValue = 'salesforce';
            }
          }
        }
      }
    }
  }
}
