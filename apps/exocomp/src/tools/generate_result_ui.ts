/*
 * Generate Result UI Tool
 *
 * This tool generates ephemeral UI for presenting results of complex operations
 * in a structured, easy-to-understand format. The UI is ephemeral and exists
 * only for the duration needed to convey the results.
 *
 * Changes made: Created new tool for result UI generation
 */

import { KnownBlock } from '@slack/bolt';
import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { clampText } from '../views/clamp_text.ts';
import { ToolCall } from './tool_call.ts';

interface MarkdownBlock {
  type: 'markdown';
  text: string;
}
type UIBlock = KnownBlock | MarkdownBlock;

export interface ResultSection {
  type: 'summary' | 'table' | 'list' | 'details';
  title?: string;
  data: any;
}

export interface GenerateResultUIArgs {
  result_type: string;
  title: string;
  description?: string;
  sections: ResultSection[];
  status: 'success' | 'partial' | 'error';
  actions_taken?: string[];
  next_steps?: string[];
}

export class GenerateResultUI extends ToolCall {
  static tool_name = 'generate_result_ui';

  private resultArgs: GenerateResultUIArgs;

  constructor(
    config: {
      logger?: Logger;
    },
    args: GenerateResultUIArgs,
  ) {
    super(config.logger);
    this.resultArgs = args;
  }

  static getPromptDescription(_: any): string {
    return (
      `You have access to a tool that generates ephemeral UI for presenting results. ` +
      `Use this when you need to display complex results in a structured format.`
    );
  }

  static getJsonSchema(_: any): Tool {
    return {
      type: 'function',
      function: {
        name: GenerateResultUI.tool_name,
        description:
          'Generate an ephemeral UI for presenting operation results',
        parameters: {
          type: 'object',
          properties: {
            result_type: {
              type: 'string',
              description:
                'The type of result being presented (e.g., "migration_results", "query_results")',
            },
            title: {
              type: 'string',
              description: 'The title of the results',
            },
            description: {
              type: 'string',
              description: 'Optional description of the results',
            },
            sections: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: {
                    type: 'string',
                    enum: ['summary', 'table', 'list', 'details'],
                    description: 'The type of section',
                  },
                  title: {
                    type: 'string',
                    description: 'Section title',
                  },
                  data: {
                    anyOf: [
                      {
                        type: 'string',
                        description:
                          'Text data for summary or details sections',
                      },
                      {
                        type: 'array',
                        items: {
                          type: 'object',
                          additionalProperties: true,
                          description:
                            'Array of objects for table or list sections',
                        },
                        description:
                          'Array of objects for table or list sections',
                      },
                      {
                        type: 'number',
                        description:
                          'Number data for summary or details sections',
                      },
                      {
                        type: 'object',
                        additionalProperties: true,
                        description:
                          'Object data for summary or details sections',
                      },
                    ],
                  },
                },
                required: ['type', 'data'],
              },
            },
            status: {
              type: 'string',
              enum: ['success', 'partial', 'error'],
              description: 'The overall status of the operation',
            },
            actions_taken: {
              type: 'array',
              items: { type: 'string' },
              description: 'List of actions that were performed',
            },
            next_steps: {
              type: 'array',
              items: { type: 'string' },
              description: 'Suggested next steps for the user',
            },
          },
          required: ['result_type', 'title', 'sections', 'status'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return '📊 Generating results display...';
  }

  getCompletionMessage(): string {
    return '✅ Results ready';
  }

  async performCall(): Promise<string> {
    return JSON.stringify({
      result_type: this.resultArgs.result_type,
      status: this.resultArgs.status,
      message: `Generated result UI for ${this.resultArgs.result_type}`,
    });
  }

  getCompletionBlocks(): KnownBlock[] {
    const blocks: UIBlock[] = [];

    // Status emoji based on status
    const statusEmoji = {
      success: '✅',
      partial: '⚠️',
      error: '❌',
    }[this.resultArgs.status];

    // Header with status
    blocks.push({
      type: 'header',
      text: {
        type: 'plain_text',
        text: clampText(`${statusEmoji} ${this.resultArgs.title}`, 150),
      },
    });

    // Description if provided
    if (this.resultArgs.description) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(this.resultArgs.description || ' ', 3000),
        },
      });
    }

    // Process each section
    this.resultArgs.sections.forEach((section) => {
      if (section.title) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: clampText(`*${section.title || ' '}*`, 3000),
          },
        });
      }

      const sectionDataIsArray = Array.isArray(section.data);

      switch (section.type) {
        case 'summary':
          blocks.push(this.createSummaryBlock(section.data));
          break;
        case 'table':
          if (sectionDataIsArray) {
            blocks.push(...this.createTableBlocks(section.data));
          } else {
            blocks.push(this.createDetailsBlock(section.data));
          }
          break;
        case 'list':
          if (sectionDataIsArray) {
            blocks.push(this.createListBlock(section.data));
          } else {
            blocks.push(this.createDetailsBlock(section.data));
          }
          break;
        case 'details':
          blocks.push(this.createDetailsBlock(section.data));
          break;
      }

      blocks.push({ type: 'divider' });
    });

    // Actions taken
    if (
      this.resultArgs.actions_taken &&
      this.resultArgs.actions_taken.length > 0
    ) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Actions Performed:*',
        },
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(
            this.resultArgs.actions_taken
              .map((action) => `• ${action}`)
              .join('\n'),
            3000,
          ),
        },
      });
    }

    // Next steps
    if (this.resultArgs.next_steps && this.resultArgs.next_steps.length > 0) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Suggested Next Steps:*',
        },
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(
            this.resultArgs.next_steps
              .map((step, idx) => `${idx + 1}. ${step}`)
              .join('\n'),
            3000,
          ),
        },
      });
    }

    return blocks as KnownBlock[];
  }

  private createSummaryBlock(data: any): UIBlock {
    if (typeof data === 'object') {
      const lines: string[] = [];
      for (const [key, value] of Object.entries(data)) {
        lines.push(`*${this.formatKey(key)}:* ${value}`);
      }
      return {
        type: 'markdown',
        text: clampText(lines.join('\n'), 12000),
      };
    }

    return {
      type: 'markdown',
      text: clampText(String(data), 12000),
    };
  }

  private createTableBlocks(data: any): UIBlock[] {
    const blocks: UIBlock[] = [];

    if (Array.isArray(data) && data.length > 0) {
      const asciiTable = createAsciiTable(data);
      blocks.push({
        type: 'markdown',
        text: clampText('```\n' + asciiTable + '\n```', 12000),
      });

      if (asciiTable.length > 12000) {
        const remainingLines = asciiTable.substring(12000).split('\n').length;
        blocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `_... and ${remainingLines} more lines_`,
            },
          ],
        });
      }
    }

    return blocks;
  }

  private createListBlock(data: any): UIBlock {
    let items: string[] = [];

    if (Array.isArray(data)) {
      items = data.map((item) => {
        if (typeof item === 'object') {
          return `- ${JSON.stringify(item)}`;
        }
        return `- ${item}`;
      });
    } else if (typeof data === 'object') {
      items = Object.entries(data).map(
        ([key, value]) => `- *${this.formatKey(key)}:* ${value}`,
      );
    }

    return {
      type: 'markdown',
      text: clampText(items.join('\n'), 12000),
    };
  }

  private createDetailsBlock(data: any): UIBlock {
    let text: string;

    if (typeof data === 'string') {
      text = data;
    } else {
      text = '```\n' + JSON.stringify(data, null, 2) + '\n```';
    }

    return {
      type: 'markdown',
      text: clampText(text || ' ', 12000),
    };
  }

  private formatKey(key: string): string {
    // Convert snake_case or camelCase to Title Case
    return key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .trim()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
}

function createAsciiTable(data: Record<string, any>[]): string {
  if (!data || data.length === 0) {
    return 'No data provided';
  }

  // Extract headers from the first object's keys
  const headers = Object.keys(data[0]);

  // Calculate the maximum width for each column
  const columnWidths = headers.map((header) =>
    Math.max(
      ...data.map((row) => row[header].toString().length),
      header.length,
    ),
  );

  // Create the divider
  const divider =
    '+' +
    headers.map((header, i) => '-'.repeat(columnWidths[i] + 2)).join('+') +
    '+';

  // Create the header row
  const headerRow =
    '|' +
    headers
      .map((header, i) => ` ${header.padEnd(columnWidths[i])} `)
      .join('|') +
    '|';

  // Generate each row of data
  const rows = data.map(
    (row) =>
      '|' +
      headers
        .map(
          (header, i) => ` ${row[header].toString().padEnd(columnWidths[i])} `,
        )
        .join('|') +
      '|',
  );

  // Assemble the full table
  return [divider, headerRow, divider, ...rows, divider].join('\n');
}
