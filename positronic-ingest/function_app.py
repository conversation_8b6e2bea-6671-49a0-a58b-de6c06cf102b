import csv
from io import BytesIO
import json
import logging
import os
import tempfile
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed

import azure.durable_functions as df
import azure.functions as func
from fastexcel import UnsupportedColumnTypeCombinationError
import pandas
import zipfile
import xml.etree.ElementTree as ET

import polars as pl
import psycopg2
import psycopg2.extras
import requests
import pdfplumber
from constants import (
    CONTENT_TYPE_DOC,
    CONTENT_TYPE_RFP,
    DOCUMENT_STATUS_ACTIVE,
    JOB_STATUS_ERROR,  # to implement,
    JOB_STATUS_PROCESSED,
    MAX_BATCH_SIZE,
    NOTIFICATION_SUBJECT,
    MIMETYPE_PPTX,
    MIMETYPE_DOCX,
    MIMETYPE_XLSX,
    KEY_COLUMN_IDX_QUESTIONS,
    KEY_COLUMN_IDX_ANSWERS,
    KEY_COLUMN_INSIGHTS,
    MAX_FILE_SIZE_BYTES,
    LARGE_FILE_MAX_PARALLEL_BATCHES,
    CHUNK_PROCESSING_BATCH_SIZE,
    CHUNK_PROCESSING_MAX_PARALLEL_BATCHES,
)
from function_pdf import process_pdf
from function_large_pdf_parallel import (
    sequence_and_store_chunks as sequence_and_store_chunks_impl,
)
from function_rfp import is_valid_rfp_config, is_valid_insights_config, process_rfp
from function_utils import process_pptx, convert_docx_to_pdf
from helper_azure import connect_azure_blob_storage, get_azure_key_vault_values
from helper_extractors import get_extractors
from helper_functions import get_contents_from_url, process_chunk
from helper_tribble import (
    get_m2m_token,
    get_all_extraction_task_configs,
    get_api_endpoint,
    get_database_connection,
    get_metadata_types_and_values,
    write_activity_log,
    write_job_log,
    get_client_batch_page_count_threshold,
)

app_auth_level = app_auth_level = (
    func.AuthLevel.ANONYMOUS
    if os.getenv("ENVIRONMENT", "") == "development"
    else func.AuthLevel.FUNCTION
)
app = df.DFApp(http_auth_level=app_auth_level)


def get_pdf_page_count(raw_content: bytes):
    """
    Get the number of pages in a PDF document.
    
    Args:
        raw_content: Raw PDF content as bytes
        
    Returns:
        Number of pages in the PDF, or None if unable to determine
    """
    try:
        with pdfplumber.open(BytesIO(raw_content)) as pdf:
            page_count = len(pdf.pages)
            logging.info(f"[get_pdf_page_count] PDF has {page_count} pages")
            return page_count
    except Exception as e:
        logging.error(f"[get_pdf_page_count] Error reading PDF: {e}")
        return None


@app.route(route="invoke", methods=["POST"])
@app.durable_client_input(client_name="client")
async def HttpStart(req: func.HttpRequest, client):
    payload: dict = req.get_json()

    # RFP or DOC
    type: str = payload.get("type", "")

    # job id -- need to update status when all done (or error occurs)
    job_id: int = payload.get("job_id", -1)

    # storage account blob URL (e.g. https://[prefix][storageid].blob.core.windows.net)
    # the storage_id will be like c000001, so it is up to the caller to construct the full URL
    # prefix will depend on environment: testing = "tribbletest", staging = "tribblestaging", prod = "trible"
    storage_url: str = payload.get("storage_url", "")

    # the "c000001"-format should be the same as the storage_url, but keep as separate param
    # for generality
    database_id: str = payload.get("database_id", "")

    # Use for activity log
    user_id: int = payload.get("user_id", -1)
    client_id: int = payload.get("client_id", -1)

    # name of container for Azure
    # should be standard, but, pass in from the caller
    folder: str = payload.get("folder", "")

    # list of files.
    # format: { "file_name": "...", "meta": {} }
    files: list[dict] = payload.get("files", [])

    # If true, this is a new version of an existing document.
    # Call api/update_doc after ingestion to complete the flow.
    is_update: bool = payload.get("is_update", False)

    # Optional params when we want to force a re-ingest.
    # This should NOT re-notify the user, and should only update things
    # if the re-ingest was successful
    is_tribble_reingest: bool = payload.get("is_tribble_reingest", False)

    user_id: str = payload.get("user_id", "")

    params = {
        "job_id": job_id,
        "storage_url": storage_url,
        "database_id": database_id,
        "user_id": user_id,
        "client_id": client_id,
        "folder": folder,
        "files": files,
        "type": type,
        "is_update": is_update,
        "is_tribble_reingest": is_tribble_reingest,
    }

    instance_id = await client.start_new("ingest", client_input=params)
    logging.info(f"Started orchestration with ID = '{instance_id}'.")

    return client.create_check_status_response(req, instance_id)


@app.orchestration_trigger(context_name="context")
def ingest(context: df.DurableOrchestrationContext):
    params: dict = context.get_input()

    type: str = params["type"]
    files: list[dict] = params["files"]
    job_id: int = params["job_id"]
    storage_url: str = params["storage_url"]
    database_id: int = params["database_id"]
    is_update: bool = params["is_update"]
    user_id: str = params["user_id"]
    is_tribble_reingest: bool = params["is_tribble_reingest"]

    # STEP 0: Cache all relevant Key-Vault values (so no need for repeated calls)
    env_vars = yield context.call_activity("get_env_vars", {})

    if (
        (type != CONTENT_TYPE_RFP and type != CONTENT_TYPE_DOC)
        or job_id == -1
        or storage_url == ""
        or database_id == ""
        or len(files) == 0
    ):
        yield context.call_activity(
            "write_to_db", params | {"error": "Invalid request."} | {"env": env_vars}
        )
        return {"error": "Invalid request."}

    preprocess_results = yield context.call_activity(
        "preprocess", params | {"env": env_vars}
    )
    params["metadata_definitions"] = preprocess_results.get("metadata_definitions", {})
    params["extraction_configs"] = preprocess_results.get("extraction_configs", {})
    params["access_token"] = preprocess_results.get("access_token", "")

    # STEP 1: create processing tasks
    tasks = []
    processed_files = []
    total_num_embeddings = 0
    chunk_batch_counter = 1

    # Get processing configuration - use defaults with batching control from environment
    batching_enabled = env_vars.get("FILE-INGEST-ENABLE-BATCHING", "false").lower() == "true"
    
    BATCH_SIZE = min(5, MAX_BATCH_SIZE)  # Use default batch size
    LARGE_FILE_MAX_PARALLEL_BATCHES_ENV = LARGE_FILE_MAX_PARALLEL_BATCHES if batching_enabled else 0
    CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV = CHUNK_PROCESSING_MAX_PARALLEL_BATCHES if batching_enabled else 0
    CHUNK_PROCESSING_BATCH_SIZE_ENV = CHUNK_PROCESSING_BATCH_SIZE

    for idx, file in enumerate(files):
        if file is not None and ("file_name" in file or "file_name_original" in file):
            params_augmented = params | {
                "file": file, 
                "env": env_vars,
                "LARGE_FILE_MAX_PARALLEL_BATCHES_ENV": LARGE_FILE_MAX_PARALLEL_BATCHES_ENV,
                "CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV": CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV,
                "CHUNK_PROCESSING_BATCH_SIZE_ENV": CHUNK_PROCESSING_BATCH_SIZE_ENV
            }

            # Step 1: Preprocess file (handle DOCX conversion if needed)
            processed_file, raw_content = preprocess_file_for_ingestion(file, params_augmented)
            
            # Step 2: Determine processing route based on file size and type
            is_large_file = should_use_parallel_processing(processed_file, raw_content, params_augmented)

            # Update params with processed file (but not raw_content for JSON serialization)
            params_augmented["file"] = processed_file

            # Check if this is a large file that should use parallel processing
            if is_large_file:
                logging.info(f"[ingest] Routing file {processed_file.get('file_name', 'unknown')} to parallel processing")
                # Route to parallel processing orchestrator
                tasks.append(context.call_sub_orchestrator("process_large_pdf_orchestrator", params_augmented))
            else:
                # Use regular processing
                tasks.append(context.call_activity("process_file", params_augmented))

            if len(tasks) >= BATCH_SIZE or idx == len(files) - 1:
                # STEP 2: extract text from files and create embeddings
                ingested_files = yield context.task_all(tasks)

                # STEP 3: for each file, iterate through its text chunks and
                # run the per-chunk processing (e.g. auto tagging, embedding).
                for _, ingested_file in enumerate(ingested_files):
                    file_chunks = []

                    file_meta = ingested_file["file"]
                    params_augmented = params | {"file": file_meta, "env": env_vars}
                    num_text_chunks = len(ingested_file["text_chunks"])

                    if CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV > 0 and num_text_chunks > CHUNK_PROCESSING_BATCH_SIZE_ENV:
                        # Parallel processing for files with many chunks
                        logging.info(f"[orchestrate_pipeline] Using parallel chunk processing for file with {num_text_chunks} chunks")
                        chunk_batches = []
                        task_chunks = []
                        batch_index = 0
                        
                        # Collect all chunk batches first
                        for idx_chunk, chunk in enumerate(ingested_file["text_chunks"]):
                            task_chunks.append(chunk)

                            if len(task_chunks) >= CHUNK_PROCESSING_BATCH_SIZE_ENV or idx_chunk == (num_text_chunks - 1):
                                chunk_batches.append({
                                    "params": params_augmented | {
                                        "chunks": task_chunks,
                                        "batch_counter": chunk_batch_counter,
                                        "batch_index": batch_index,
                                    },
                                    "batch_index": batch_index,
                                })
                                task_chunks = []
                                chunk_batch_counter += 1
                                batch_index += 1
                        
                        # Process batches in parallel groups
                        logging.info(f"[orchestrate_pipeline] Processing {len(chunk_batches)} chunk batches in groups of {CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV}")
                        batch_results = []
                        for group_start in range(0, len(chunk_batches), CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV):
                            batch_group = chunk_batches[group_start:group_start + CHUNK_PROCESSING_MAX_PARALLEL_BATCHES_ENV]
                            
                            # Fan-out: Process batch group in parallel
                            group_tasks = []
                            for batch in batch_group:
                                group_tasks.append(
                                    context.call_activity("process_file_chunks", batch["params"])
                                )
                            
                            # Fan-in: Collect results
                            group_results = yield context.task_all(group_tasks)
                            
                            # Store results with batch index for ordering
                            for i, result in enumerate(group_results):
                                batch_results.append({
                                    "batch_index": batch_group[i]["batch_index"],
                                    "chunks": result["chunks"]
                                })
                        
                        # Sort results by batch index and aggregate chunks
                        batch_results.sort(key=lambda x: x["batch_index"])
                        for batch_result in batch_results:
                            total_num_embeddings += len(batch_result["chunks"])
                            file_chunks.extend(batch_result["chunks"])
                    
                    else:
                        # Sequential processing for small files (existing logic)
                        task_chunks = []
                        for idx_chunk, chunk in enumerate(ingested_file["text_chunks"]):
                            task_chunks.append(chunk)

                            if len(task_chunks) >= BATCH_SIZE or idx_chunk == (
                                num_text_chunks - 1
                            ):
                                processed_chunks = yield context.call_activity(
                                    "process_file_chunks",
                                    params_augmented
                                    | {
                                        "chunks": task_chunks,
                                        "batch_counter": chunk_batch_counter,
                                    },
                                )

                                total_num_embeddings += len(processed_chunks["chunks"])
                                file_chunks.extend(processed_chunks["chunks"])
                                task_chunks = []
                                chunk_batch_counter += 1

                    filename = (
                        file_meta["file_name_original"]
                        if "file_name_original" in file_meta
                        else file_meta["file_name"]
                    )

                    processed_files.append(
                        {
                            "file": ingested_file["file"],
                            "filename": filename,
                            "chunks": file_chunks,
                        }
                    )

                    # FUTURE: we're doing chunk-level processing.
                    # For tree / hierarchy-like indexing, need to then look holistically
                    # across the whole document.

                    # STEP 4: save each file as it's processed
                    extra_params = {
                        "file_chunks": file_chunks,
                        "is_final": idx == len(files) - 1,
                    }

                    if ingested_file.get("error", None) is not None:
                        extra_params["error"] = ingested_file["error"]

                    if idx == len(files) - 1:
                        extra_params["total_num_embeddings"] = total_num_embeddings
                        extra_params["total_num_files"] = len(processed_files)

                    yield context.call_activity(
                        "write_to_db", params_augmented | extra_params
                    )

                tasks = []

    if len(processed_files) == 0:
        return {"num_files": 0}

    # STEP 4: Update old version if necessary
    if is_update:
        yield context.call_activity(
            "update_doc_version",
            {"payload": {"database_id": database_id, "files": files}, "env": env_vars},
        )

    # STEP 5: Send notification (Exception if it's a re-ingest)
    if not is_tribble_reingest:
        message = ""
        if type == CONTENT_TYPE_RFP:
            if len(processed_files) > 1:
                message = (
                    f"{len(processed_files)} past questionnaires have been processed"
                )
            else:
                processed_file = processed_files[0]["file"]
                processed_file_name = (
                    processed_file["file_name_original"]
                    if "file_name_original" in processed_file
                    else processed_file["file_name"]
                )
                message = f"""One past questionnaire ({processed_file_name}) has been processed"""

        else:
            if len(processed_files) > 1:
                message = f"{len(processed_files)} documents have been processed"
            else:
                processed_file = processed_files[0]["file"]
                processed_file_name = (
                    processed_file["file_name_original"]
                    if "file_name_original" in processed_file
                    else processed_file["file_name"]
                )
                message = (
                    f"""One past document ({processed_file_name}) has been processed"""
                )

        yield context.call_activity(
            "send_notification",
            {"payload": {"message": message, "user_id": user_id}, "env": env_vars},
        )

    return {"num_files": len(processed_files), "num_embeddings": total_num_embeddings}

def get_raw_file(file: dict, params: dict) -> bytes:
    """
    Get the raw file from the file metadata.
    """
    storage_url: str = params["storage_url"]
    folder: str = params["folder"]
    file_name_original: str = file.get("file_name", "")

    blob_service_client = connect_azure_blob_storage(storage_url)

    blob_client = blob_service_client.get_blob_client(
        container=folder, blob=file_name_original
        )
    downloader = blob_client.download_blob(max_concurrency=1)

    return downloader.readall()


def preprocess_file_for_ingestion(file: dict, params: dict) -> tuple[dict, bytes]:
    """
    Preprocess files before ingestion - handle DOCX to PDF conversion if needed.
    
    Args:
        file: File metadata dictionary
        params: Processing parameters
        
    Returns:
        Tuple of (modified file metadata with conversion status, raw file content)
    """
    file_name = file.get("file_name", "unknown")
    file_mimetype = file.get("file_mimetype", "")
    document_id = file.get("document_id", -1)
    
    logging.info(f"[preprocess_file_for_ingestion] Preprocessing file: {file_name} (mimetype: {file_mimetype})")
    
    # Get the raw file content first (always needed)
    raw = get_raw_file(file, params)
    
    # Only process DOCX files for conversion
    if file_mimetype != MIMETYPE_DOCX:
        logging.info(f"[preprocess_file_for_ingestion] File {file_name} does not need conversion")
        return (file, raw)
    
    try:
        logging.info(f"[preprocess_file_for_ingestion] Converting DOCX to PDF: {file_name}")
        
        # Convert DOCX to PDF
        raw_pdf = convert_docx_to_pdf(raw, params.get("env", {}))
        
        if raw_pdf is None:
            logging.error(f"[preprocess_file_for_ingestion] Failed to convert DOCX to PDF: {file_name}")
            raise Exception(f"Failed to convert DOCX to PDF: {file_name}")
        
        # Handle file conversion (upload PDF and update metadata)
        storage_url = params["storage_url"]
        folder = params["folder"]
        
        converted_uuid = handle_file_conversion(
            raw_pdf,
            file_name,
            storage_url,
            folder,
            document_id,
            params,
            "docx",
        )
        
        # Mark file as converted and update file metadata with new UUID filename
        file_copy = file.copy()
        file_copy["was_converted_from_docx"] = True
        file_copy["file_name"] = converted_uuid  # Update to use UUID filename for blob access
        
        logging.info(f"[preprocess_file_for_ingestion] Successfully converted DOCX to PDF: {file_name}")
        return (file_copy, raw_pdf)
        
    except Exception as e:
        logging.error(f"[preprocess_file_for_ingestion] Error preprocessing file {file_name}: {e}")
        raise Exception(f"File preprocessing failed for {file_name}: {e}")


def should_use_parallel_processing(file: dict, raw_content: bytes, params: dict) -> bool:
    """
    Determine if a file should use parallel processing based on page count.
    
    Args:
        file: File metadata (should already be preprocessed)
        raw_content: Raw file content (to avoid re-downloading)
        params: Processing parameters
        
    Returns:
        True if file should use parallel processing, False otherwise
    """
    file_name = file.get("file_name", "unknown")
    file_mimetype = file.get("file_mimetype", "")

    logging.info(f"[should_use_parallel_processing] Evaluating file: {file_name}")
    
    # Check if parallel processing is enabled
    max_parallel_batches = params.get("LARGE_FILE_MAX_PARALLEL_BATCHES_ENV") 
    if max_parallel_batches is None:
        # This should not happen as we now pass it from the orchestrator
        # but provide a fallback for safety
        max_parallel_batches = LARGE_FILE_MAX_PARALLEL_BATCHES
    
    if max_parallel_batches <= 0:
        logging.info(f"[should_use_parallel_processing] File {file_name} rejected: parallel processing disabled (max_parallel_batches={max_parallel_batches})")
        return False

    # Check if it's a PDF file or DOCX (which gets converted to PDF)
    # Note: DOCX files should already be converted to PDF in preprocessing step
    is_pdf_processable = (
        file_mimetype == "application/pdf" or 
        file_mimetype == MIMETYPE_DOCX or
        file.get("was_converted_from_docx", False)
    )
    
    if not is_pdf_processable:
        logging.info(f"[should_use_parallel_processing] File {file_name} rejected: not a PDF or DOCX file (mimetype='{file_mimetype}')")
        return False

    # Get page count from the file to determine if it should use parallel processing
    try:
        # Get client-specific batch page count threshold
        database_id = params.get("database_id", "")
        env = params.get("env", {})
        
        if database_id and env:
            batch_page_count_threshold = get_client_batch_page_count_threshold(database_id, env)
        else:
            # Fallback to default if we can't get client setting
            batch_page_count_threshold = 100  # Default 100 pages
            logging.warning(f"[should_use_parallel_processing] Could not get client setting, using default threshold: {batch_page_count_threshold} pages")
        
        # Extract page count from PDF
        page_count = get_pdf_page_count(raw_content)
        
        if page_count is None:
            logging.warning(f"[should_use_parallel_processing] Could not determine page count for {file_name}, defaulting to regular processing")
            return False
        
        # Use client-specific or default threshold to determine if file should use parallel processing
        logging.info(f"[should_use_parallel_processing] Evaluating file {file_name}: pages={page_count}, threshold={batch_page_count_threshold} pages")
        
        # Special case: if threshold is 0, always use parallel processing for eligible files
        if batch_page_count_threshold == 0:
            is_large_file = True
            logging.info(f"[should_use_parallel_processing] File {file_name} will use parallel processing (threshold set to 0 - parallel processing enabled for all files)")
        else:
            is_large_file = page_count >= batch_page_count_threshold
            if is_large_file:
                logging.info(f"[should_use_parallel_processing] File {file_name} has many pages ({page_count} pages >= {batch_page_count_threshold} pages), routing to parallel processing")
            else:
                logging.info(f"[should_use_parallel_processing] File {file_name} has few pages ({page_count} pages < {batch_page_count_threshold} pages), using regular processing")
        
        logging.info(f"[should_use_parallel_processing] Final decision for {file_name}: {'PARALLEL' if is_large_file else 'REGULAR'} processing")
        return is_large_file
        
    except Exception as e:
        logging.error(f"[should_use_parallel_processing] Error checking page count for {file.get('file_name', 'unknown')}: {e}")
        # If we can't check the page count, default to regular processing
        logging.info(f"[should_use_parallel_processing] File {file.get('file_name', 'unknown')} rejected: error checking page count, defaulting to regular processing")
        return False


@app.orchestration_trigger(context_name="context")
def process_large_pdf_orchestrator(context: df.DurableOrchestrationContext):
    """
    Orchestrator for parallel processing of large PDF files.
    
    This orchestrator implements the fan-out/fan-in pattern to process
    large PDFs in parallel batches, significantly reducing processing time
    and avoiding timeout issues.
    """
    params = context.get_input()
    
    document_id = params["file"]["document_id"]
    file_name = params["file"].get("file_name", "unknown")
    file_mimetype = params["file"].get("file_mimetype", "unknown")
    
    logging.info(f"[process_large_pdf_orchestrator] Starting parallel processing for document {document_id}")
    
    try:
        # STEP 1: Split PDF into batch definitions
        batches = yield context.call_activity("split_pdf_into_batches", params)
        
        if not batches:
            raise ValueError("No batches created for parallel processing")
        
        total_batches = len(batches)
        logging.info(f"[process_large_pdf_orchestrator] Created {total_batches} batches for parallel processing")
        
        # STEP 2: Limit concurrent batches to avoid overwhelming the system
        # Get configuration from parameters (passed from orchestrator)
        LARGE_FILE_MAX_PARALLEL_BATCHES_ENV = params.get("LARGE_FILE_MAX_PARALLEL_BATCHES_ENV", LARGE_FILE_MAX_PARALLEL_BATCHES)
        max_parallel = min(total_batches, LARGE_FILE_MAX_PARALLEL_BATCHES_ENV)
        
        if total_batches > max_parallel:
            logging.info(f"[process_large_pdf_orchestrator] Processing in chunks of {max_parallel} parallel batches")
        
        all_batch_results = []
        
        # Process batches in groups to respect concurrency limits
        for batch_start in range(0, total_batches, max_parallel):
            batch_end = min(batch_start + max_parallel, total_batches)
            current_batch_group = batches[batch_start:batch_end]
            
            logging.info(f"[process_large_pdf_orchestrator] Processing batch group {batch_start + 1}-{batch_end} ({len(current_batch_group)} batches)")
            
            # STEP 3: Fan-out - Launch parallel batch processing
            batch_tasks = []
            for batch in current_batch_group:
                task = context.call_activity("process_pdf_batch_parallel", batch)
                batch_tasks.append(task)
            
            # STEP 4: Fan-in - Collect batch results
            batch_group_results = yield context.task_all(batch_tasks)
            all_batch_results.extend(batch_group_results)
        
        # STEP 5: Sequence chunks and store to database
        sequencing_params = {
            "batch_results": all_batch_results,
            "document_metadata": params,
        }
        
        final_result = yield context.call_activity("sequence_and_store_chunks", sequencing_params)
        
        # Add summary statistics
        successful_batches = final_result["successful_batches"]
        failed_batches = final_result["failed_batches"]
        total_chunks = final_result["total_chunks"]
        
        logging.info("[process_large_pdf_orchestrator] Parallel processing completed:")
        logging.info(f"  - Document: {file_name} (ID: {document_id})")
        logging.info(f"  - Total batches: {total_batches}")
        logging.info(f"  - Successful batches: {successful_batches}")
        logging.info(f"  - Failed batches: {failed_batches}")
        logging.info(f"  - Total chunks: {total_chunks}")
        
        return {
            "text_chunks": final_result["chunks"],
            "file": params["file"],
            "parallel_processing_stats": {
                "total_batches": total_batches,
                "successful_batches": successful_batches,
                "failed_batches": failed_batches,
                "total_chunks": total_chunks,
                "deduplication_count": final_result["deduplication_count"],
            }
        }
        
    except Exception as e:
        error_msg = f"Parallel processing failed for document {document_id}: {e}"
        logging.error(f"[process_large_pdf_orchestrator] {error_msg}")
        
        # Log detailed error information for debugging
        logging.error("[process_large_pdf_orchestrator] Error details:")
        logging.error(f"  - File: {file_name}")
        logging.error(f"  - Document ID: {document_id}")
        logging.error(f"  - Total batches planned: {len(batches) if 'batches' in locals() else 'Unknown'}")
        logging.error(f"  - Exception type: {type(e).__name__}")
        logging.error(f"  - Exception message: {str(e)}")
        
        # Try to get partial results if batch processing partially succeeded
        partial_results = []
        if 'all_batch_results' in locals() and all_batch_results:
            successful_batch_results = [batch for batch in all_batch_results if batch.get("success", False)]
            if successful_batch_results:
                logging.info(f"[process_large_pdf_orchestrator] Attempting to recover {len(successful_batch_results)} successful batches")
                try:
                    sequencing_params = {
                        "batch_results": successful_batch_results,
                        "document_metadata": params,
                    }
                    partial_result = yield context.call_activity("sequence_and_store_chunks", sequencing_params)
                    partial_results = partial_result.get("chunks", [])
                    logging.info(f"[process_large_pdf_orchestrator] Recovered {len(partial_results)} chunks from successful batches")
                except Exception as recovery_error:
                    logging.error(f"[process_large_pdf_orchestrator] Recovery attempt failed: {recovery_error}")
        
        return {
            "text_chunks": partial_results,
            "file": params["file"],
            "error": error_msg,
            "partial_success": len(partial_results) > 0,
            "recovered_chunks": len(partial_results),
        }


@app.activity_trigger(input_name="params")
def get_env_vars(params: dict):
    isLocalDev = os.getenv("ENVIRONMENT", "") == "development"

    env_vars_list = [
        "OPENAI-API-KEY",
        "DATABASEURL",
        "SLACK-NOTIFICATION-URL",
        "API-ENDPOINT",
        "AUTH0-AUDIENCE",
        "AUTH0-ENDPOINT",
        "M2M-SECRET-1",
        "AZURE-OPENAI-CONFIG",
        "FORM-RECOGNIZER-ENDPOINT-LOCAL-DEV" if isLocalDev else "FORM-RECOGNIZER-ENDPOINT",
        "FORM-RECOGNIZER-KEY-LOCAL-DEV" if isLocalDev else "FORM-RECOGNIZER-KEY",
        # Single secret to control batching behavior
        "FILE-INGEST-ENABLE-BATCHING",
    ]

    env_vars = {}
    values = get_azure_key_vault_values(env_vars_list)

    for idx, value in enumerate(values):
        if value == "":
            raise Exception(f"Missing environment variable: {env_vars_list[idx]}")
        else:
            env_vars[env_vars_list[idx]] = value

    return env_vars


@app.activity_trigger(input_name="params")
def preprocess(params: dict):
    logging.info("[preprocess] Starting...")

    access_token = get_m2m_token(params["env"])

    user_id: int = params["user_id"]
    client_id: int = params["client_id"]
    job_id: int = params["job_id"]
    files: int = params["files"]

    conn = get_database_connection(params["env"])
    cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

    logging.info("[preprocess] Connection created")

    # Log the start into the Activity Log
    write_activity_log(
        user_id, client_id, job_id, "start", {}, {"num_files": len(files)}, cursor, conn
    )

    conn.commit()

    metadata_definitions = get_metadata_types_and_values(cursor, params["database_id"])
    extraction_configs = get_all_extraction_task_configs(cursor, params["database_id"])

    cursor.close()
    conn.close()

    # Also get the metadata_filter definitions names/descriptions
    return {
        "metadata_definitions": metadata_definitions,
        "extraction_configs": extraction_configs,
        "access_token": access_token,
    }


@app.activity_trigger(input_name="params")
def process_file(params: dict):
    logging.info("[process_file] Starting...")

    storage_url: str = params["storage_url"]
    folder: str = params["folder"]
    file: dict = params["file"]
    type: str = params["type"]

    file_name: str = file.get("file_name", "")
    file_mimetype: str = file.get("file_mimetype", "")
    file_meta: dict = file.get("meta", {})
    document_id: int = file.get("document_id", -1)

    if file_name == "" or document_id == -1:
        return {"text_chunks": [], "file": file}

    # Check if raw content is already available from preprocessing
    raw = params.get("raw_content")
    
    if raw is None:
        # Fall back to downloading if not available (shouldn't happen in normal flow)
        try:
            logging.info("[process_file] Raw content not provided, downloading from blob...")
            blob_service_client = connect_azure_blob_storage(storage_url)
            blob_client = blob_service_client.get_blob_client(container=folder, blob=file_name)
            downloader = blob_client.download_blob(max_concurrency=1)
            raw = downloader.readall()
        except Exception as e:
            logging.error(f"Error downloading blob: {e}")
            return {
                "text_chunks": [],
                "file": file,
                "error": f"Error downloading blob: {e}",
            }
    else:
        logging.info("[process_file] Using preprocessed raw content...")

    logging.info(f"[process_file] Processing document_id [{document_id}]...")
    logging.info(f"[process_file] File length: [{len(raw)}]...Mimetype [{file_mimetype}]")

    # Check file size limits against maximum allowed (2GB limit is global)
    file_size = len(raw)
    max_file_size = MAX_FILE_SIZE_BYTES
    
    # Reject files over maximum size
    if file_size > max_file_size:
        logging.error(
            f"[process_file] Document_id [{document_id}] exceeds maximum size: {file_size} bytes. Skipping..."
        )
        return {
            "text_chunks": [],
            "file": file,
            "error": f"File too large ({file_size} bytes). Maximum allowed is {max_file_size} bytes.",
        }
    
    # Note: Parallel processing routing decisions are made by the orchestrator
    # Files that reach this function should be processed synchronously
    logging.info(f"[process_file] Processing document_id [{document_id}] synchronously (file_size: {file_size} bytes)")

    if raw is not None:
        if type == CONTENT_TYPE_DOC:
            if file_mimetype == MIMETYPE_PPTX:
                try:
                    pptx_raw = process_pptx(raw, file_meta, document_id, params)

                    if (pptx_raw is None) or (len(pptx_raw) != 2):
                        return {"text_chunks": [], "file": file}
                    else:
                        text_chunks, raw = pptx_raw
                        handle_file_conversion(
                            raw,
                            file_name,
                            storage_url,
                            folder,
                            document_id,
                            params,
                            "pptx",
                        )
                        return {"text_chunks": text_chunks, "file": file}
                except Exception as e:
                    logging.error(
                        f"[process_file] ({file_name}) Error processing pptx: {e}"
                    )
                    return {
                        "text_chunks": [],
                        "file": file,
                        "error": f"Error processing pptx: {e}",
                    }

            else:
                try:
                    # Process PDF/DOCX synchronously (orchestrator already made routing decision)
                    # Note: DOCX files should already be converted to PDF in preprocessing step
                    if file_mimetype == MIMETYPE_DOCX or file.get("was_converted_from_docx", False):
                        logging.info(f"[process_file] Processing DOCX (converted to PDF): {file_name} (document_id={document_id})")
                    else:
                        logging.info(f"[process_file] Processing PDF: {file_name} (document_id={document_id})")
                    
                    text_chunks = process_pdf(raw, file_meta, document_id, params)
                    return {"text_chunks": text_chunks, "file": file}
                except Exception as e:
                    logging.error(
                        f"[process_file] ({file_name}) Error processing doc: {e}"
                    )
                    return {
                        "text_chunks": [],
                        "file": file,
                        "error": f"Error processing doc: {e}",
                    }

        elif is_valid_rfp_config(file_meta) or is_valid_insights_config(file_meta):
            try:
                raw_bytes = BytesIO(raw)
                df = None

                if file_mimetype == MIMETYPE_XLSX:
                    logging.info(
                        f"[process_file] Parsing XLSX with sheet_id={file_meta.get('sheet_id', 1)}"
                    )

                    # Do a little gymnastics to read from the XLSX, and export to CSV
                    # (remember the process_rfp is expecting Bytes)
                    sheet_id = file_meta.get("sheet_id", 1)
                    sheet_name = get_sheet_name_by_id(raw_bytes, sheet_id)

                    # See: https://github.com/pola-rs/polars/issues/20376
                    # Until the polars calamime engine supports drop_empty_cols, we'll use pandas
                    # Note: switching to the xlsx2csv engine has its quirks too.

                    # df = read_excel_with_fallback(
                    #     raw_bytes, sheet_name=sheet_name, is_structured=False
                    # )

                    header_row_idx = file_meta.get("header_row_idx", -1)
                    df = pandas.read_excel(
                        raw_bytes,
                        sheet_name=sheet_name,
                        na_filter=False,
                        header=None,
                        skiprows=None if header_row_idx == -1 else (header_row_idx + 1),
                    )

                    # See https://stackoverflow.com/questions/52846672/python-pandas-head-attribute-not-working
                    if sheet_name is None:
                        logging.info("[process_file] Single sheet XLSX detected: extracting first sheet")
                        df = list(df.values())[0]                    

                    # Useful for debugging, so I'll keep it.
                    logging.info("[process_file] First 15 rows:\n")
                    logging.info(df.head(15))

                    raw = BytesIO()
                    df.to_csv(raw, header=False, index=False)
                    raw.seek(0)
                    raw = raw.read()

                else:
                    logging.info("[process_file] Parsing CSV")

                    question_columns_index = file_meta.get(
                        KEY_COLUMN_IDX_QUESTIONS, None
                    )
                    answer_columns_index = file_meta.get(KEY_COLUMN_IDX_ANSWERS, None)

                    insight_columns_index = file_meta.get(KEY_COLUMN_INSIGHTS, None)

                    max_col_idx = 0
                    if insight_columns_index is not None and len(insight_columns_index) > 0:
                        max_col_idx = max(insight_columns_index)
                    elif question_columns_index and answer_columns_index:
                        combined_columns = question_columns_index + answer_columns_index
                        if len(combined_columns) > 0:
                            max_col_idx = max(combined_columns)
                        else:
                            max_col_idx = 0
                    else:
                        max_col_idx = 0

                    stringSchema = {}
                    for i in range(max_col_idx + 1):
                        stringSchema[str(i)] = pl.String

                    df = pl.read_csv(
                        raw_bytes,
                        has_header=False,
                        schema=stringSchema,
                        truncate_ragged_lines=True,
                    )

                    # Useful for debugging, so I'll keep it.
                    logging.info("[process_file] First 15 rows:\n")
                    logging.info(df.head(15))

                    raw = BytesIO()
                    df.write_csv(raw, include_header=False)
                    raw.seek(0)
                    raw = raw.read()

                text_chunks = process_rfp(raw, file_meta, document_id, params)
                return {"text_chunks": text_chunks, "file": file}
            except Exception as e:
                logging.error(f"[process_file] ({file_name}) Error processing rfp: {e}")
                return {
                    "text_chunks": [],
                    "file": file,
                    "error": f"Error processing rfp: {e}",
                }

    return {"text_chunks": [], "file": file}


@app.activity_trigger(input_name="params")
def process_file_chunks(params: dict):
    file = params["file"]
    chunks = params["chunks"]
    batch_counter = params["batch_counter"]

    params["extractor_functions"] = get_extractors(file)

    if batch_counter == 1:
        logging.info(
            f"[process_file_chunks] Using [{len(params['extractor_functions'])}] extractors"
        )

    # sanity check
    if len(chunks) == 0:
        logging.info(
            f"[process_file_chunks] BATCH #{batch_counter} has no chunks. Skipping..."
        )
        return {"chunks": []}

    logging.info(f"[process_file_chunks] PROCESSING BATCH #{batch_counter}")

    BATCH_SIZE = min(len(chunks), MAX_BATCH_SIZE)

    processed_chunks = []
    with ThreadPoolExecutor(max_workers=BATCH_SIZE) as executor:
        # Submit each row for processing
        future_to_row = {
            executor.submit(process_chunk, chunk, file, params): chunk
            for _, chunk in enumerate(chunks)
        }

        for future in as_completed(future_to_row):
            result = future.result()
            if result:
                processed_chunks.append(result)

    return {"chunks": processed_chunks}


@app.activity_trigger(input_name="params")
def write_to_db(params: dict):
    logging.info("[write_to_db] Started...")

    try:
        conn = get_database_connection(params["env"])
        cursor = conn.cursor()

        logging.info("[write_to_db] Connection created")

        job_id = params["job_id"]
        database_id = params["database_id"]
        is_tribble_reingest = params["is_tribble_reingest"]

        if params.get("error", None) is None:
            user_id: int = params["user_id"]
            client_id: int = params["client_id"]
            is_final: bool = params["is_final"]

            file = params["file"]
            file_chunks = params["file_chunks"]

            # For final write
            total_num_embeddings = params.get("total_num_embeddings", 0)
            total_num_files = params.get("total_num_files", 0)

            logging.info(f"[write_to_db] # file_chunks={len(file_chunks)}")

            document_ids = {}
            records_to_write = []

            results = []

            for file_chunk in file_chunks:
                embedding_result = file_chunk["embedding_result"]
                document_ids[file["document_id"]] = True

                if (
                    embedding_result is not None
                    and len(embedding_result["embedding"]) > 0
                    and embedding_result["embedding"][0] != "NaN"
                ):
                    records_to_write.append(embedding_result)

            results.append(
                {
                    "file_name": file["file_name"],
                    "num_embeddings": len(file_chunks),
                }
            )

            logging.info(
                f"[write_to_db] # embeddings / records_to_write={len(records_to_write)}"
            )

            if len(records_to_write) > 0:
                # Convert document IDs to integers
                doc_ids_int = [int(doc_id) for doc_id in document_ids]

                # if re-ingest, then update any prior document embeddings use_for_generation = false
                if is_tribble_reingest:
                    logging.info(
                        f"[write_to_db] Re-ingest detected. Updating prior embeddings to use_for_generation=false for doc ids: {doc_ids_int}"
                    )

                    cursor.execute(
                        f"""
                            UPDATE {database_id}.embedding
                            SET use_for_generation = false
                            WHERE document_id = ANY(%s)
                        """,
                        [(doc_ids_int,)],
                    )

                # Extract source dates from file metadata if available and add to records
                source_created_date = None
                source_modified_date = None
                if "meta" in file and file["meta"]:
                    source_created_date = file["meta"].get("source_created_date")
                    source_modified_date = file["meta"].get("source_modified_date")
                # Add source date to each record if it exists
                if source_created_date:
                    for record in records_to_write:
                        record["source_created_date"] = source_created_date
                if source_modified_date:
                    for record in records_to_write:
                        record["source_modified_date"] = source_modified_date

                # Create DataFrame after adding any additional fields
                df = pandas.DataFrame(records_to_write)

                temp_csv_file = os.path.join(
                    tempfile.gettempdir(), str(time.time()).replace(".", "") + ".csv"
                )
                logging.info(f"[write_to_db] Writing to [{temp_csv_file}]")

                df.to_csv(
                    temp_csv_file,
                    header=False,
                    index=False,
                    doublequote=True,
                    escapechar=None,  # Don't use escape character to avoid \\ issues
                    quoting=csv.QUOTE_ALL,
                )

                logging.info("[write_to_db] Start COPY...")

                # Build a more dynamic COPY command based on available source dates
                columns = [
                    "document_id",
                    "index",
                    "origin",
                    "content",
                    "content_type",
                    "content_meta",
                    "embedding",
                    "use_for_generation",
                    "metadata_filter",
                ]

                if source_created_date:
                    columns.append("source_created_date")

                if source_modified_date:
                    columns.append("source_modified_date")

                columns_str = ", ".join(columns)

                copy_sql = f""" COPY {database_id}.embedding 
                      ({columns_str})
                    FROM STDIN 
                      WITH CSV DELIMITER ',' QUOTE '\"' NULL AS 'null'
                """
                with open(
                    temp_csv_file, mode="r", encoding="utf-8", errors="ignore"
                ) as csv_file:
                    cursor.copy_expert(copy_sql, csv_file)

                # Also write embedding_history records
                logging.info(
                    f"[write_to_db] Writing history records for: {document_ids}"
                )

                try:
                    history_sql = f"""
                        INSERT INTO {database_id}.embedding_history (embedding_id, user_id, history_date, history_content, history_metadata_filter)
                        SELECT e.id, {user_id}, now(), e.content, e.metadata_filter
                        FROM {database_id}.embedding e
                        JOIN {database_id}.document d ON e.document_id = d.id
                        WHERE d.id = ANY(%s)
                    """
                    cursor.execute(history_sql, [(doc_ids_int,)])
                except Exception as e:
                    # continue on, although weird if this fails
                    logging.error(f"[write_to_db] Error writing history records: {e}")

                conn.commit()

            logging.info(
                f"[write_to_db] Wrote [{len(records_to_write)}] records to the DB (job_id={job_id}, document_ids={list(document_ids.keys())})"
            )

            # Not sure if we'd actually ever have multiple doc_ids here, but just in case
            if (len(document_ids.keys())) > 0:
                cursor.execute(
                    f"UPDATE {database_id}.document SET status = %s WHERE id IN %s",
                    (DOCUMENT_STATUS_ACTIVE, tuple(document_ids.keys())),
                )
            else:
                # In this case, there was probably nothing written, but set the doc to active anyway
                cursor.execute(
                    f"UPDATE {database_id}.document SET status = %s WHERE id = %s",
                    (DOCUMENT_STATUS_ACTIVE, file["document_id"]),
                )

            # Log the start into the Activity Log and Job Record if last batch
            if is_final:
                cursor.execute(
                    f"UPDATE {database_id}.job SET status = %s WHERE id = %s",
                    (JOB_STATUS_PROCESSED, job_id),
                )
                write_activity_log(
                    user_id,
                    client_id,
                    job_id,
                    "end",
                    {},
                    {
                        "num_files": total_num_files,
                        "num_embeddings": total_num_embeddings,
                    },
                    cursor,
                    conn,
                )

            conn.commit()
            conn.close()

            logging.info("[write_to_db] Updated job status")
            return {"success": True, "results": results}

        else:
            logging.error(
                f"[write_to_db] Error detected: {params.get('error', 'Unknown error')}"
            )

            error_message = params.get("error", "Unknown error occurred.")
            error_id = str(uuid.uuid4())

            if database_id == "":
                logging.error(
                    f"[write_to_db] Missing required parameter: database_id (Error Id={error_id})"
                )
            else:
                logging.error(
                    f"[write_to_db] An error occurred while processing: (Error Id={error_id}) {error_message}"
                )

                # Write detailed error information to job_log table
                write_job_log(
                    job_id=job_id,
                    log_type="error",
                    data={
                        "error_id": error_id,
                        "error_message": error_message,
                        "error_context": "document_processing",
                        "is_tribble_reingest": is_tribble_reingest,
                        "database_id": database_id,
                    },
                    cursor=cursor,
                    conn=conn,
                    database_id=database_id,
                )

                # Use generic user-friendly message for job table
                user_error_message = (
                    "An error occurred while processing your document. Please contact support if this issue persists."
                    if not is_tribble_reingest
                    else ""
                )

                # If a re-ingest, assume the prior job was actually successful,
                # so just update the status back to processed
                cursor.execute(
                    f"""
                        UPDATE {database_id}.job 
                        SET status = %s, error = %s
                        WHERE id = %s""",
                    (
                        (
                            JOB_STATUS_ERROR
                            if not is_tribble_reingest
                            else JOB_STATUS_PROCESSED
                        ),
                        user_error_message,
                        job_id,
                    ),
                )

                conn.commit()
                conn.close()

            # Technically success is True? But...just in a handled error state??
            return {
                "success": False,
                "error": f"{error_message} (Error Id={error_id})",
            }

    except Exception as e:
        error_id = str(uuid.uuid4())

        logging.error(f"[write_to_db] Exception encountered={e} (Error Id={error_id})")

        # Try to write to job_log if we have the necessary parameters
        if 'job_id' in params and 'database_id' in params:
            try:
                conn = get_database_connection(params["env"])
                cursor = conn.cursor()
                write_job_log(
                    job_id=params["job_id"],
                    log_type="error",
                    data={
                        "error_id": error_id,
                        "error_message": str(e),
                        "error_context": "unhandled_exception",
                        "function": "write_to_db",
                        "database_id": params["database_id"],
                    },
                    cursor=cursor,
                    conn=conn,
                    database_id=params["database_id"],
                )
                cursor.close()
                conn.close()
            except Exception as log_error:
                logging.error(f"[write_to_db] Failed to write error to job_log: {log_error}")

        return {
            "success": False,
            "error": f"An unhandled exception occurred. (Error Id={error_id})",
        }


@app.activity_trigger(input_name="params")
def send_notification(params: dict):
    try:
        env: dict = params["env"]
        payload: dict = params["payload"]

        message: str = payload["message"]
        user_id: str = payload["user_id"]
        subject = (
            NOTIFICATION_SUBJECT  # Only applicable if user gets notifications by email
        )

        token = get_m2m_token(env)
        headers = {"m2m-secret": f"{token}"}
        api_endpoint = get_api_endpoint(env)
        url = api_endpoint + "/notify_user"

        data = {"user_id": user_id, "message": message, "subject": subject}
        response = requests.post(url, json=data, headers=headers)
        response_json = response.json()

        logging.info(f"Notification: user_id: {user_id}, {response_json}")
        return response_json

    except Exception as e:
        logging.error(f"Error calling /api/notify_user: {e}")
        return {"success": False}


@app.activity_trigger(input_name="params")
def update_doc_version(params: dict):
    try:
        env: dict = params["env"]
        payload: dict = params["payload"]
        schema = payload["database_id"]
        files: list[dict] = payload["files"]

        token = get_m2m_token(env)
        headers = {"m2m-secret": f"{token}"}
        api_endpoint = get_api_endpoint(env)
        url = api_endpoint + "/update_doc"

        responses = {}

        for f in files:
            new_doc_id: int = f.get("document_id", -1)
            data = {"new_doc_id": new_doc_id, "schema": schema}
            response = requests.post(url, json=data, headers=headers)
            responses[new_doc_id] = response.json()

        logging.info(
            f"[update_doc_version] Updated doc version on {len(responses.keys())} document(s)"
        )
        return {}

    except Exception as e:
        logging.error(f"[update_doc_version] Error calling /api/update_doc: {e}")
        return {"success": False}


### SYNCHRONOUS ENDPOINT - Parse a PDF by URL ###
@app.route(route="version", methods=["GET"])
def get_version(req: func.HttpRequest):
    try:
        with open('version.json', 'r') as f:
            version_data = json.load(f)
        return func.HttpResponse(
            json.dumps(version_data),
            mimetype="application/json"
        )
    except FileNotFoundError:
        # Fallback if version.json doesn't exist
        return func.HttpResponse(
            json.dumps({"error": "Version information not available"}),
            status_code=404,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"[get_version] Error: {e}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )


@app.route(route="get_pdf", methods=["POST"])
def HttpStartSync(req: func.HttpRequest):
    try:
        params: dict = req.get_json()
        pdf_url: str = params.get("url", None)

        if pdf_url is None:
            logging.error("[get_pdf] Missing required parameter: url")
            return ""

        pdf_contents = get_contents_from_url(pdf_url)
        if pdf_contents != "":
            pages = process_pdf(pdf_contents, {}, -1, params)

            pdf_text = ""
            for page in pages:
                pdf_text += page["text"] + " "

            return pdf_text
        else:
            return ""

    except Exception as e:
        logging.error(f"[get_pdf] Error: {e}")
        return ""


@app.route(route="file_to_dataframe", methods=["POST"])
def file_to_dataframe(req: func.HttpRequest) -> func.HttpResponse:
    """
    Converts Excel files to polars dataframes. Returns as Parquet files which can be read by polars.

    Returns:
        func.HttpResponse: The HTTP response object containing the combined Parquet file.
    """
    result = {"errors": {}}
    combined_parquet = BytesIO()

    try:
        for input_file in req.files.values():
            try:
                extension = input_file.filename.split(".")[-1]
                sheet_id = int(req.form.get("sheet_id", "1"))

                if extension == "xlsx" or extension == "xls":
                    raw_bytes = input_file.stream.read()
                    sheet_bytes = BytesIO(raw_bytes)
                    input_file.seek(0)
                    sheet_name = get_sheet_name_by_id(sheet_bytes, sheet_id)
                    if sheet_name is None:
                        # Fall back to sheet_id as index
                        sheets = pandas.ExcelFile(sheet_bytes).sheet_names
                        sheet_name = sheets[sheet_id - 1]

                    if sheet_name is None:
                        raise Exception(f"Sheet with id {sheet_id} could not found")

                    df = read_excel_with_fallback(
                        input_file, sheet_name=sheet_name, is_structured=True
                    )

                elif extension == "csv":
                    df = pl.read_csv(
                        input_file.stream.read(), truncate_ragged_lines=True
                    )
                # Filter out null columns:
                df = df[[s.name for s in df if not (s.null_count() == df.height)]]

                df.write_parquet(combined_parquet)
            except Exception as e:
                logging.warn(f"[file_to_dataframe] Error parsing Excel file: {e}")
                result["errors"][input_file.filename] = str(e)

        if len(result["errors"]) > 0:
            return func.HttpResponse(
                json.dumps(result),
                status_code=400,
                mimetype="application/json",
            )

        combined_parquet.seek(0)
        if combined_parquet.getbuffer().nbytes == 0:
            logging.warn("[xl_to_dataframe] No data written to buffer.")
            return func.HttpResponse(body="No data", status_code=400)

        return func.HttpResponse(
            body=combined_parquet.getvalue(),
            status_code=200,
            mimetype="application/octet-stream",
        )

    except Exception as e:
        logging.error(f"[xl_to_dataframe] General error: {e}")
        return func.HttpResponse(
            "Bad request",
            status_code=400,
        )


def read_excel_with_fallback(
    file_data, sheet_name, string_columns=None, is_structured=False
):
    string_columns = string_columns or {}
    try:
        # Handle both BytesIO and file objects with stream attribute
        data = (
            file_data.stream.read()
            if hasattr(file_data, "stream")
            else file_data.getvalue()
        )
        if is_structured:
            return pl.read_excel(
                BytesIO(data),
                sheet_name=sheet_name,
                schema_overrides=string_columns,
            )
        else:
            return pl.read_excel(
                BytesIO(data),
                sheet_name=sheet_name,
                schema_overrides=string_columns,
                drop_empty_cols=False,
                drop_empty_rows=False,
                has_header=False,
            )
    except UnsupportedColumnTypeCombinationError as e:
        if "could not determine dtype for column" in str(e):
            # Reset position for both types of objects
            if hasattr(file_data, "stream"):
                file_data.seek(0)
            # Extract the column name from the error message
            col_name = str(e).split("\n")[2].split("dtype for column ")[1]
            # Add this column to our string_columns dict
            string_columns[col_name] = pl.String
            # Retry with the updated string_columns
            return read_excel_with_fallback(
                file_data,
                sheet_name,
                string_columns=string_columns,
                is_structured=is_structured,
            )
        raise


def create_file_with_metadata(raw_pdf, file_name):
    return {
        "buffer": raw_pdf,
        "uuid": str(uuid.uuid4()),
        "file_name": file_name,
    }


def upload_pdf_to_blob(file_with_metadata, storage_url, container_name):
    try:
        blob_service_client = connect_azure_blob_storage(storage_url)
        container_client = blob_service_client.get_container_client(container_name)

        blob_client = container_client.get_blob_client(file_with_metadata["uuid"])

        blob_client.upload_blob(file_with_metadata["buffer"], overwrite=True)
        logging.info(
            f"[upload_pdf_to_blob] Uploaded converted PDF: {file_with_metadata['file_name']}"
        )
        return True
    except Exception as e:
        logging.error(f"[upload_pdf_to_blob] Error uploading converted PDF: {e}")
        return False


def update_document_metadata(cursor, database_id, document_id, file_type, preview_uuid):
    try:
        cursor.execute(
            f"""
            UPDATE {database_id}.document 
            SET file_type = %s, preview_uuid = %s
            WHERE id = %s
            """,
            (file_type, preview_uuid, document_id),
        )
        logging.info(
            f"[update_document_metadata] Updated metadata for document {document_id}"
        )
        return True
    except Exception as e:
        logging.error(
            f"[update_document_metadata] Error updating document metadata: {e}"
        )
        return False


def handle_file_conversion(
    raw, file_name, storage_url, folder, document_id, params, new_file_type
):
    new_filename = f"{file_name.rsplit('.', 1)[0]}.{new_file_type}"
    file_with_metadata = create_file_with_metadata(raw, new_filename)

    if upload_pdf_to_blob(file_with_metadata, storage_url, folder):
        conn = get_database_connection(params["env"])
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        try:
            if not update_document_metadata(
                cursor,
                params["database_id"],
                document_id,
                new_file_type,
                file_with_metadata["uuid"],
            ):
                logging.warning(
                    f"[handle_file_conversion] Failed to update metadata for document {document_id}"
                )
            else:
                conn.commit()
        finally:
            cursor.close()
            conn.close()
    else:
        logging.warning(
            f"[handle_file_conversion] Failed to upload converted file for {new_filename}"
        )

    return file_with_metadata["uuid"]


# The sheet_id doesn't always correspond to the sheet_name index.
# So we go spelunking in the workbook.xml
def get_sheet_name_by_id(raw_bytes, sheet_id):
    target_sheet_name = None
    try:
        with zipfile.ZipFile(raw_bytes) as zf:
            wb_xml = zf.read("xl/workbook.xml")
            root = ET.fromstring(wb_xml)
            ns = {"ns": "http://schemas.openxmlformats.org/spreadsheetml/2006/main"}
            sheets = root.findall(".//ns:sheet", ns)
            for sheet in sheets:
                if sheet.get("sheetId") == str(sheet_id):
                    target_sheet_name = sheet.get("name")
                    break
    except Exception as e:
        logging.error(f"[get_sheet_name_by_id] Error getting sheet name: {e}")
        return None
    finally:
        return target_sheet_name


# Parallel processing activity functions

@app.activity_trigger(input_name="params")
def split_pdf_into_batches(params: dict):
    """Activity function to split PDF into batch definitions for parallel processing."""
    start_time = time.time()
    file_name = params.get("file", {}).get("file_name", "unknown")
    
    logging.info(f"[split_pdf_into_batches] Starting batch planning for: {file_name}")
    
    try:
        from function_large_pdf_parallel_optimized import split_and_upload_pdf_batches as split_func
        result = split_func(params)
        
        processing_time = time.time() - start_time
        logging.info(f"[split_pdf_into_batches] Completed in {processing_time:.2f}s - created {len(result)} batches for {file_name}")
        
        return result
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Failed to split PDF into batches: {e}"
        logging.error(f"[split_pdf_into_batches] {error_msg} (failed after {processing_time:.2f}s)")
        logging.error(f"[split_pdf_into_batches] File: {file_name}")
        logging.error(f"[split_pdf_into_batches] Exception type: {type(e).__name__}")
        raise Exception(error_msg)


@app.activity_trigger(input_name="params")
def process_pdf_batch_parallel(params: dict):
    """Activity function to process a single PDF batch in parallel."""
    start_time = time.time()
    batch_number = params.get("batch_number", "unknown")
    file_name = params.get("file", {}).get("file_name", "unknown")
    page_start = params.get("page_start", 0)
    page_end = params.get("page_end", 0)
    
    logging.info(f"[process_pdf_batch_parallel] Starting batch {batch_number} for {file_name} (pages {page_start + 1}-{page_end})")
    
    try:
        from function_large_pdf_parallel_optimized import process_pdf_batch_optimized as process_func
        result = process_func(params)
        
        processing_time = time.time() - start_time
        success = result.get("success", False)
        chunk_count = result.get("chunk_count", 0)
        
        if success:
            logging.info(f"[process_pdf_batch_parallel] Batch {batch_number} completed in {processing_time:.2f}s - produced {chunk_count} chunks")
        else:
            logging.warning(f"[process_pdf_batch_parallel] Batch {batch_number} failed in {processing_time:.2f}s - {result.get('error', 'unknown error')}")
        
        return result
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Activity function error for batch {batch_number}: {e}"
        logging.error(f"[process_pdf_batch_parallel] {error_msg} (failed after {processing_time:.2f}s)")
        logging.error(f"[process_pdf_batch_parallel] File: {file_name}")
        logging.error(f"[process_pdf_batch_parallel] Pages: {page_start + 1}-{page_end}")
        logging.error(f"[process_pdf_batch_parallel] Exception type: {type(e).__name__}")
        
        # Return error result instead of raising exception to allow other batches to continue
        return {
            "batch_number": batch_number,
            "page_start": page_start,
            "page_end": page_end,
            "chunks": [],
            "chunk_count": 0,
            "success": False,
            "error": error_msg,
            "processing_time": processing_time,
        }


@app.activity_trigger(input_name="params")
def sequence_and_store_chunks(params: dict):
    """Activity function to sequence chunks from parallel processing and store to database."""
    start_time = time.time()
    batch_results = params.get("batch_results", [])
    
    logging.info(f"[sequence_and_store_chunks] Starting sequencing of {len(batch_results)} batch results")
    
    try:
        result = sequence_and_store_chunks_impl(params)
        
        processing_time = time.time() - start_time
        total_chunks = result.get("total_chunks", 0)
        successful_batches = result.get("successful_batches", 0)
        failed_batches = result.get("failed_batches", 0)
        
        logging.info(f"[sequence_and_store_chunks] Completed in {processing_time:.2f}s")
        logging.info(f"[sequence_and_store_chunks] Results: {total_chunks} chunks from {successful_batches} successful / {failed_batches} failed batches")
        
        # Clean up temporary container if it exists
        if batch_results and len(batch_results) > 0:
            first_batch = batch_results[0]
            if "temp_container" in first_batch and "storage_url" in first_batch:
                try:
                    from function_large_pdf_parallel_optimized import cleanup_temp_container
                    cleanup_temp_container(first_batch["storage_url"], first_batch["temp_container"])
                    logging.info("[sequence_and_store_chunks] Cleaned up temporary container")
                except Exception as cleanup_error:
                    logging.warning(f"[sequence_and_store_chunks] Failed to cleanup temp container: {cleanup_error}")
        
        return result
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Failed to sequence and store chunks: {e}"
        logging.error(f"[sequence_and_store_chunks] {error_msg} (failed after {processing_time:.2f}s)")
        logging.error(f"[sequence_and_store_chunks] Batch results count: {len(batch_results)}")
        logging.error(f"[sequence_and_store_chunks] Exception type: {type(e).__name__}")
        raise Exception(error_msg)
