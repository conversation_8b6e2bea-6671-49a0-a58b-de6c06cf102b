import json
import logging
import os
from typing import Dict, List, TypedDict

import psycopg2
import requests

from constants import CONTENT_TYPE_INSIGHT, LLM_PROXY_TIMEOUT
from helper_functions import retry_request


def get_database_connection(env: dict):
    connection_string = os.getenv("DATABASEURL", env["DATABASEURL"])
    return psycopg2.connect(connection_string)


def get_api_endpoint(env: dict):
    if os.getenv("ENVIRONMENT", "") == "development":
        return os.getenv("API_ENDPOINT", "http://localhost:3000/api")
    else:
        return env["API-ENDPOINT"]


# replicating retry logic form helper_functions::retry_request
# should try to standardize (although this one uses a SDK vs. the raw request of the other)
# Could pass in a func I suppose...
def get_m2m_token(env: dict):
    return env.get("M2M-SECRET-1", None)


class LLMParams:
    client_id: str
    user_id: str
    schema: str
    content_detail_id: int
    needs_redaction: bool
    system_prompt: dict
    human_prompt: dict
    type: str


def ask_llm(params: LLMParams, env, access_token):
    try:
        data = {
            "user_id": params["user_id"],
            "client_id": params["client_id"],
            "schema": params["schema"],
            "input_messages": [params["system_prompt"], params["human_prompt"]],
            "needs_redaction": params["needs_redaction"],
            "source_table": "content_detail",
            "source_id": params["content_detail_id"],
            "type": params["type"],
        }

        headers = {"m2m-secret": f"{access_token}"}
        api_endpoint = get_api_endpoint(env)
        url = api_endpoint + "/call_llm"

        response = requests.post(url, json=data, headers=headers)
        response_json = response.json()
        return response_json

    except Exception as e:
        logging.error(f"[ask_llm] Exception: {e}")
        return {}


def ask_llm_proxy(
    chat_completion: dict, params: dict, llm_call_type: str, event_details: str = ""
):
    try:
        env = params["env"]
        tribble_params = {
            "client_id": params["client_id"],
            "user_id": params["user_id"],
            "schema": params["database_id"],
            "job_id": params["job_id"],
            "needs_redaction": False,
            "type": llm_call_type,
            "timeout": LLM_PROXY_TIMEOUT,
        }
        tribble_payload = {
            "schema": tribble_params["schema"],
            "needs_redaction": tribble_params["needs_redaction"],
            "timeout": (
                tribble_params["timeout"] if "timeout" in tribble_params else 120000
            ),
            "activity_log_data": {
                "client_id": tribble_params["client_id"],
                "user_id": tribble_params["user_id"],
                "source_table": "job",
                "source_id": tribble_params["job_id"],
                "type": tribble_params["type"],
                "event": event_details,
            },
        }

        data = {
            **chat_completion,  # Add all dict keys/values from chat_completion dict.
            "tribble_payload": tribble_payload,
        }

        call_llm_proxy_api_key = os.getenv("CALL_LLM_PROXY_API_KEY")
        headers = {
            "api-key": call_llm_proxy_api_key,
        }

        api_endpoint = get_api_endpoint(env)
        llm_oai_proxy_endpoint = os.getenv("LLM_OAI_PROXY_ENDPOINT")
        oai_api_verion = os.getenv("AGENT_API_VERSION_LATEST")

        if llm_oai_proxy_endpoint is None or oai_api_verion is None:
            logging.error("[ask_llm_proxy] LLM_OAI_PROXY_ENDPOINT env var is not set.")
            return {}

        url = api_endpoint + llm_oai_proxy_endpoint + oai_api_verion

        # Log the request
        logging.info(f"[ask_llm_proxy] Request: {url}\n{headers}\n{data}")

        response = retry_request(url, data, headers)
        if response is None:
            logging.info(f"[ask_llm_proxy] Response is None.")
            return {}

        response_json = response.json()
        return response_json

    except Exception as e:
        logging.error(f"[ask_llm_proxy] Exception: {e}")
        return {}


def write_job_log(
    job_id: int,
    log_type: str,
    data: dict,
    cursor,
    conn,
    database_id: str,
):
    """Write detailed error information to the job_log table."""
    try:
        cursor.execute(
            f"""
                INSERT INTO {database_id}.job_log
                (job_id, type, data, created_date) 
                VALUES
                (%s, %s, %s, NOW())""",
            [
                job_id,
                log_type,
                json.dumps(data),
            ],
        )
        conn.commit()
        logging.info(f"[write_job_log] Job log record written: {log_type} for job {job_id}")
    except Exception as e:
        logging.error(f"[write_job_log] Failed to write job log: {e}")
        # Don't re-raise - job log failure shouldn't break the main flow


def write_activity_log(
    user_id: int,
    client_id: int,
    job_id: int,
    event: str,
    token_usage: dict,
    details: dict,
    cursor,
    conn,
):
    cursor.execute(
        f"""
            INSERT INTO tribble.activity_log
            (client_id, user_id, date_timestamp, source_table, source_id, type, event, token_usage, details) 
            VALUES
            (%s, %s, NOW(), 'job', %s, 'ingest', %s, %s, %s)""",
        [
            client_id,
            user_id,
            job_id,
            event,
            json.dumps(token_usage),
            json.dumps(details),
        ],
    )

    logging.info(f"[write_activity_log] Activity Log record written: ingest [{event}]")


def get_all_extraction_task_configs(cursor, schema):
    try:
        query = f"""
                SELECT * FROM {schema}.extraction_task_config
                WHERE is_enabled = TRUE
                ORDER BY id DESC"""

        cursor.execute(query)
        extraction_configs = cursor.fetchall()
        logging.info(
            f"[get_all_extraction_task_configs] Found {len(extraction_configs)} extraction configs"
        )

        return extraction_configs

    except Exception as e:
        logging.error(f"[get_all_extraction_task_configs] Exception: {e}")
        return []


class MetadataValue(TypedDict, total=False):
    id: int
    label: str
    description: str
    synonyms: str
    is_active: bool


class MetadataType(TypedDict, total=False):
    id: int
    name: str


MetadataValues = Dict[str, List[MetadataValue]]


class MetadataResult(TypedDict):
    metadata_types: Dict[str, MetadataType]
    metadata_values: Dict[str, List[MetadataValues]]


def get_metadata_types_and_values(cursor, schema) -> MetadataResult:
    try:
        # Fetch metadata types
        query_types = f"""
						SELECT  
                        mdf_type.name as type_name, 
                        mdf_type.is_content_access as is_content_access, 
                        mdf_type.is_verbatim as is_verbatim,
                        mdf.*
						FROM    {schema}.metadata_filter mdf
						JOIN    {schema}.metadata_filter_type mdf_type
								on mdf_type.id = mdf.type_id
						ORDER BY 
								mdf_type.id, is_active, mdf.id
				"""
        cursor.execute(query_types)
        metadata_values = cursor.fetchall()

        if metadata_values is None or len(metadata_values) == 0:
            logging.info("[get_metadata_types_and_values] No metadata types found")
            return {}

        mdf_types = {}
        mdf_values = {}

        # Organize metadata values under their respective types
        for value in metadata_values:
            type_name = value["type_name"]

            mdf_types[type_name] = {
                "name": type_name,
                "id": value["type_id"],
                "is_content_access": value["is_content_access"] or False,
            }

            if mdf_values.get(type_name, None) is None:
                mdf_values[type_name] = []

            mdf_values[type_name].append(
                {
                    "id": value["id"],
                    "label": value["value"],
                    "is_active": value["is_active"],
                    "synonyms": value["synonyms"] or "",
                    "description": value["description"],
                }
            )

        return {"metadata_types": mdf_types, "metadata_values": mdf_values}
    except:
        logging.error(
            "[get_metadata_types_and_values] Failed to fetch metadata types and values"
        )
        return {}


def get_embeddings(chunk, file, params):
    file_meta = file["meta"]
    document_id = file["document_id"]
    type = params["type"]
    use_for_generation = True

    is_insight = chunk.get("is_insight", False)
    if is_insight:
        type = CONTENT_TYPE_INSIGHT
        use_for_generation = False

    print(f"[get_embeddings] Started for chunk [{chunk['index']}]")

    token = params["access_token"]
    env = params["env"]

    def call_embedding_endpoint(text, token):
        headers = {"m2m-secret": f"{token}"}
        api_endpoint = get_api_endpoint(env)
        url = api_endpoint + "/generate_embedding"

        data = {"text": text}

        keep_trying = True
        counter = 0
        MAX_COUNTER = 3

        embeddings = None

        while keep_trying and counter < MAX_COUNTER:
            try:
                response = requests.post(url, json=data, headers=headers)
                embeddings = response.json()
                keep_trying = False
            except Exception as e:
                print(
                    f"[call_embedding_endpoint] /generate_embedding endpoint returned an error: {e}"
                )
                counter += 1

        if embeddings is not None:
            return embeddings
        else:
            return {"success": False}

    def create_embedding_result(
        index, text, embedding, content_type, metadata, metadata_filter, document_id, use_for_generation
    ):
        # Ensure large file metadata is preserved in content_meta
        content_meta = metadata.copy()
        
        # Add large file specific fields if present
        if metadata.get("is_large_file_batch", False):
            content_meta["is_large_file"] = True
            content_meta["batch_number"] = metadata.get("batch_number")
            content_meta["total_batches"] = metadata.get("total_batches")
            content_meta["total_pages"] = metadata.get("total_pages")
            if metadata.get("has_cross_batch_context", False):
                content_meta["has_cross_batch_context"] = True
        
        # Sanitize string content to prevent JSON escape sequence issues
        def sanitize_text_for_json(text):
            """Clean text content to prevent JSON parsing issues."""
            if not isinstance(text, str):
                return text
            
            import re
            
            # Use regex to handle all invalid escapes at once
            # Keep valid JSON escapes, replace others
            valid_escapes = {'"', '\\', '/', 'b', 'f', 'n', 'r', 't', 'u'}
            
            def replace_invalid_escape(match):
                char = match.group(1)
                # Handle unicode escapes separately
                if char == 'u':
                    # Look ahead to check if it's a valid unicode escape
                    start_pos = match.start()
                    if start_pos + 6 <= len(text):
                        hex_part = text[start_pos+2:start_pos+6]
                        if len(hex_part) == 4 and all(c in '0123456789abcdefABCDEF' for c in hex_part):
                            return match.group(0)  # Valid unicode escape, keep it
                    return char  # Invalid unicode escape, replace with just 'u'
                return f'\\{char}' if char in valid_escapes else char
            
            original_text = text
            text = re.sub(r'\\(.)', replace_invalid_escape, text)
            
            # Debug logging if changes were made
            if original_text != text:
                logging.info(f"[sanitize_text_for_json] Cleaned escape sequences in text")
            
            return text
        
        def sanitize_object_for_json(obj):
            """Recursively sanitize all string content in an object."""
            if isinstance(obj, dict):
                return {k: sanitize_object_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [sanitize_object_for_json(item) for item in obj]
            elif isinstance(obj, str):
                return sanitize_text_for_json(obj)
            else:
                return obj
        
        # Sanitize content before JSON serialization
        sanitized_content_meta = sanitize_object_for_json(content_meta)
        sanitized_metadata_filter = sanitize_object_for_json(metadata_filter)
        
        # Use json.dumps with proper error handling
        try:
            content_meta_json = json.dumps(sanitized_content_meta, ensure_ascii=False, separators=(',', ':'))
        except (TypeError, ValueError) as e:
            logging.warning(f"[create_embedding_result] Error serializing content_meta, using fallback: {e}")
            # Fallback: create a simpler version without problematic fields
            safe_content_meta = {k: str(v) if not isinstance(v, (dict, list)) else {} for k, v in sanitized_content_meta.items()}
            content_meta_json = json.dumps(safe_content_meta, ensure_ascii=False, separators=(',', ':'))
        
        try:
            metadata_filter_json = json.dumps(sanitized_metadata_filter, ensure_ascii=False, separators=(',', ':'))
        except (TypeError, ValueError) as e:
            logging.warning(f"[create_embedding_result] Error serializing metadata_filter, using fallback: {e}")
            metadata_filter_json = json.dumps({}, ensure_ascii=False, separators=(',', ':'))
        
        return {
            "document_id": document_id,
            "index": index,
            "origin": "AZURE",
            "content": text,
            "content_type": content_type,
            "content_meta": content_meta_json,
            "embedding": embedding,
            "use_for_generation": use_for_generation,
            "metadata_filter": metadata_filter_json,
        }

    embedding_text = chunk["embedding_text"].strip()

    if len(embedding_text) == 0:
        print("[get_embeddings] Empty embedding text.")
        return None

    # This would have been populated by the tagging extractors
    chunk_metadata_filters = chunk.get("metadata_filter", {})
    mdf_types = (
        params.get("metadata_definitions", {}).get("metadata_types", {}).values()
    )
    # Prepend the embedding text with any MDF tag values.
    # E.g. "For Orchestrator, <rest of text>"
    # "For On-Premise and Cloud, <rest of text>"

    if len(chunk_metadata_filters.keys()) > 0:
        tags = []

        # Don't prepend content access or verbatim tags
        for key, value in chunk_metadata_filters.items():
            mdf_type = {}
            for mdf in mdf_types:
                if mdf.get("id") == key:
                    mdf_type = mdf
                    break

            if (
                mdf_type.get("is_content_access", False) is False
                and mdf_type.get("is_verbatim", False) is False
            ):
                tags.extend(value)

        if len(tags) == 1:
            embedding_text = f"""For {tags[0]}: {embedding_text}"""
        elif len(tags) == 2:
            embedding_text = f"""For {tags[0]} and {tags[1]}: {embedding_text}"""
        else:
            embedding_text = (
                f"""For {', '.join(tags[:-1])}, and {tags[-1]}: {embedding_text}"""
            )

    response = call_embedding_endpoint(embedding_text, token)

    if response["success"]:
        embedding = response["response"]["embedding"]
        return create_embedding_result(
            chunk.get("index", 0),
            chunk["text"],
            embedding,
            type,
            chunk.get("extra_info", {}) | file_meta,
            chunk_metadata_filters,
            document_id,
            use_for_generation
        )
    else:
        print(
            f"[get_embeddings] Generating embedding failed for text = [{embedding_text[0:50]}...]"
        )
        return None


# Not sure how to break this up.
# In general, auto tagging I want to treat as a first-class citizen
# (vs. some other customized extraction step).
# Why? One simple answer: because it needs to run the "Are you sure"? follow-on prompt
def get_client_batch_page_count_threshold(database_id: str, env: dict) -> int:
    """
    Get client-specific batch page count threshold setting.
    
    Args:
        database_id: Client database ID (e.g., 'c000001')
        env: Environment variables
        
    Returns:
        Batch page count threshold, defaults to 100 pages
    """
    DEFAULT_THRESHOLD_PAGES = 100  # 100 pages default
    
    try:
        conn = get_database_connection(env)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Query to get client-specific batch page count threshold setting
        query = f"""
            SELECT cs.value_number
            FROM {database_id}.client_setting cs
            JOIN tribble.setting s ON s.id = cs.setting_id
            WHERE s.name = 'client_batch_page_count_threshold'
              AND cs.value_number IS NOT NULL
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if result and result.get('value_number') is not None:
            threshold_pages = int(result['value_number'])
            if threshold_pages == 0:
                # Special case: 0 pages means parallel process all files regardless of page count
                logging.info(f"[get_client_batch_page_count_threshold] Using client-specific threshold: {threshold_pages} pages (parallel processing enabled for all files) for {database_id}")
                return 0
            else:
                logging.info(f"[get_client_batch_page_count_threshold] Using client-specific threshold: {threshold_pages} pages for {database_id}")
                return threshold_pages
        else:
            logging.info(f"[get_client_batch_page_count_threshold] Using default threshold: {DEFAULT_THRESHOLD_PAGES} pages for {database_id}")
            return DEFAULT_THRESHOLD_PAGES
            
    except Exception as e:
        logging.error(f"[get_client_batch_page_count_threshold] Error fetching client setting for {database_id}: {e}")
        logging.info(f"[get_client_batch_page_count_threshold] Falling back to default threshold: {DEFAULT_THRESHOLD_PAGES} pages")
        return DEFAULT_THRESHOLD_PAGES


def auto_tag_content(text: str, params: dict):
    # "metadata_definitions" were added in function_app::preprocess
    all_mdf_types = params.get("metadata_definitions", {})

    # If not MDFs, then nothing for auto tag to do
    if len(all_mdf_types) == 0 or "metadata_types" not in all_mdf_types:
        print(f"[auto_tag_content] No metadata definitions found.")
        return {}

    mdf_types = all_mdf_types["metadata_types"]
    mdf_values = all_mdf_types["metadata_values"]
    system_prompt_mdf = []

    # Construct the MDF JSON/list used in the system prompt.
    # (E.g. here are all the metadata filter types and their values that are active)
    for mdf_type in mdf_types:
        if mdf_types[mdf_type].get("is_content_access", False):
            continue
        mdf_type_values = [
            mdf_value
            for mdf_value in mdf_values[mdf_type]
            if mdf_value.get("is_active", False)
        ]

        system_prompt_mdf.append(
            {
                "tag_type": mdf_type,
                "labels": [
                    {
                        "label": mdf_value["label"],
                        "description": mdf_value.get("description", ""),
                        "synonyms": mdf_value.get("synonyms", ""),
                    }
                    for mdf_value in mdf_type_values
                ],
            }
        )

    # If there's a way to tweak this to get reliable results in 1 call...that would be great.
    # But, testing seemed to indicate unreliable.
    system_prompt = (
        "You are a helpful research assistant at your company. "
        "You are tasked with tagging content with metadata. "
        "Each metadata has a tag type and a list of labels. "
        "Each label may have synonyms and a description. "
        "A metadata tag type is applicable if and only if "
        "the given context explicitly mentions one or more of its labels: "
        "either the label itself is mentioned or any of its synonyms; "
        "or, the context references the label's description. "
        "If you identify a metadata tag type that is applicable, "
        "only include its labels that are explicitly mentioned in the context. "
        "The label MUST be one of the applicable values defined in the JSON below. "
        "Do not output a synonym value. "
        "For each label, also provide a confidence score for how confident you feel the label is mentioned: "
        "0 = not confident, 1 = very confident. "
        "If a label or its synonyms/descriptions are not explicitly mentioned in the context, "
        "they should not be included in the output. "
        "If none of the labels for a given metadata tag type are explicitly mentioned in the given context, "
        "DO NOT include the metadata tag type in the output. "
        "Below are metadata tag types and their respective labels used by your company."
        f"""\n\n{json.dumps({ "metadata_tag_types": system_prompt_mdf })}"""
    )

    human_prompt = (
        "Extract applicable metadata tag types and labels that are explicitly mentioned "
        "in the following context.\n\n"
        f'CONTEXT: "{text}"\n\n'
        "OUTPUT:"
    )

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": human_prompt},
    ]

    # Temperature 0.1 seemed to work better, but this is all voodoo...
    prompt_payload = {
        "temperature": 0.2,
        "messages": messages,
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "extract_metadata_tags",
                    "description": "Extract metadata tag types and tag type labels explicitly mentioned in a given context.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "metadata_tag_types": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "tag_type": {
                                            "type": "string",
                                            "description": "Metadata tag type",
                                        },
                                        "labels": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "label": {
                                                        "type": "string",
                                                        "description": "Explicitly mentioned tag type label",
                                                    },
                                                    "score": {
                                                        "type": "number",
                                                        "description": "A confidence score for the extracted label: 0 is not confident, 1 is very confident",
                                                    },
                                                    "explanation": {
                                                        "type": "string",
                                                        "description": "Provide an explanation for why the tag type label was extracted",
                                                    },
                                                },
                                                #     "type": "string",
                                                #     "description": "Explicitly mentioned tag type label",
                                            },
                                        },
                                    },
                                },
                            }
                        },
                        "required": ["metadata_tag_types"],
                    },
                },
            }
        ],
        "tool_choice": {
            "type": "function",
            "function": {"name": "extract_metadata_tags"},
        },
    }

    batch_counter = params["batch_counter"]

    def get_auto_tags(prompt_payload, params, log_type, log_message):
        llm_response = ask_llm_proxy(prompt_payload, params, log_type, log_message)

        try:
            response_message = llm_response["choices"][0]["message"]
            if "tool_calls" in response_message:
                return response_message["tool_calls"]
            else:
                return []

        except:
            print(
                f"[auto_tag_content::get_auto_tags] Error in LLM response: {llm_response}"
            )
            return []

    # We run this twice: second time is a "Are you sure?" prompt, which seems to
    # lead to better results

    tags_pass_1 = get_auto_tags(
        prompt_payload, params, "auto_tagger", f"Batch {batch_counter}: Initial call"
    )

    # Run the "Are you sure?" prompt
    messages.extend(
        [
            {"role": "assistant", "content": None, "tool_calls": tags_pass_1},
            {
                "role": "tool",
                "tool_call_id": tags_pass_1[0]["id"],
                "content": "Tags extracted.",
            },
            {
                "role": "user",
                "content": "Are you sure? Verify the tags you outputted, and their labels, scores, and explanations and ensure they actually apply to the given context.",
            },
        ]
    )

    tags_pass_2 = get_auto_tags(
        prompt_payload,
        params,
        "auto_tagger",
        f"Batch {batch_counter}: 2nd call",
    )

    auto_tags = {}
    if len(tags_pass_2) > 0:

        final_tags = []
        try:
            # If this errors out, then an invalid JSON was returned
            auto_tags_raw = json.loads(tags_pass_2[0]["function"]["arguments"])

            if (
                "metadata_tag_types" in auto_tags_raw
                and len(auto_tags_raw["metadata_tag_types"]) > 0
            ):
                final_tags = auto_tags_raw["metadata_tag_types"]
        except:
            pass

        for extracted_mdf_type in final_tags:
            # Need to save as { 'some_mdf_id': [string list of mdf values] }
            mdf_type_id = mdf_types.get(extracted_mdf_type["tag_type"], {}).get(
                "id", -1
            )

            if mdf_type_id != -1 and len(extracted_mdf_type.get("labels", [])) > 0:
                extracted_labels = extracted_mdf_type.get("labels", [])

                auto_tagged_labels = []
                for extracted_label in extracted_labels:
                    label = extracted_label.get("label", "")
                    score = extracted_label.get("score", 0)

                    if score == 1 and label != "":
                        auto_tagged_labels.append(label)

                if len(auto_tagged_labels) > 0:
                    auto_tags[str(mdf_type_id)] = auto_tagged_labels

    return auto_tags
