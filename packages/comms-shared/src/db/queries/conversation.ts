import { Cartridge } from '@tribble/conversation-service';
import { ClientSchema } from '@tribble/tribble-db';
import {
  Conversation,
  ConversationId,
  NewConversation,
} from '@tribble/tribble-db/clients/Conversation';
import {
  ConversationDetailId,
  ConversationDetailUpdate,
} from '@tribble/tribble-db/clients/ConversationDetail';
import type {
  ConversationSummary,
  NewConversationSummary,
} from '@tribble/tribble-db/clients/ConversationSummary';
import { getClient, getDB, query } from '@tribble/tribble-db/db_ops';
import { sql } from 'kysely';
import {
  ConversationContext,
  ConversationDetailSources,
  ConversationMessage,
  ConversationState,
} from '~/model';
import constants from '../../constants';
import { Statistics } from '../../llm';

export type ConversationRecord = Pick<
  Conversation,
  | 'id'
  | 'channel'
  | 'message_id'
  | 'user_id'
  | 'embedding_ids'
  | 'created_date'
  | 'last_activity_date'
> & {
  sources?: ConversationContext[];
};

export async function getConversationByMessageIdAndChannel(
  schema: ClientSchema,
  channel: string,
  threadOrMessageId: string,
): Promise<ConversationRecord | null> {
  try {
    const db = await getDB();

    const result = await db
      .withSchema(schema)
      .selectFrom('conversation as c')
      .select([
        'c.id',
        'c.channel',
        'c.message_id',
        'c.user_id',
        'c.embedding_ids',
        'c.created_date',
        'c.last_activity_date',
        'c.sources',
      ])
      .where('c.channel', '=', channel)
      .where('c.message_id', '=', threadOrMessageId)
      .executeTakeFirst();

    if (!result) {
      return null;
    }

    return {
      ...result,
      sources:
        (result.sources as any[])
          ?.filter(Boolean)
          .map((row: any) => new ConversationContext(row)) ?? [],
    } as ConversationRecord;
  } catch (err) {
    console.error(`[getConversationByMessageIdAndChannel] ${err.message}`);
    throw err;
  }
}

export async function getConversationById(
  schema: ClientSchema,
  conversation_id: ConversationId,
): Promise<ConversationRecord | null> {
  try {
    const db = await getDB();

    const result = await db
      .withSchema(schema)
      .selectFrom('conversation as c')
      .select([
        'c.id',
        'c.channel',
        'c.message_id',
        'c.user_id',
        'c.embedding_ids',
        'c.created_date',
        'c.last_activity_date',
        'c.sources',
      ])
      .where('c.id', '=', conversation_id)
      .executeTakeFirst();

    if (!result) {
      return null;
    }

    return {
      ...result,
      sources:
        (result.sources as any[])?.map(
          (row: any) => new ConversationContext(row),
        ) ?? [],
    } as ConversationRecord;
  } catch (err) {
    console.error(`[getConversationById] ${err.message}`);
    throw err;
  }
}

export async function insertNewConversation(
  schema: string,
  conversation: NewConversation,
): Promise<ConversationRecord | null> {
  const db = await getDB();

  const result = await db
    .withSchema(schema)
    .insertInto('conversation')
    .values({
      channel: conversation.channel,
      system: conversation.system,
      message_id: conversation.message_id,
      user_id: conversation.user_id,
      created_date: conversation.created_date || new Date(),
      last_activity_date: conversation.last_activity_date || new Date(),
      embedding_ids: conversation.embedding_ids,
      sources: JSON.stringify(conversation.sources),
    })
    .returningAll()
    .executeTakeFirst();

  if (!result) {
    return null;
  }

  return {
    ...result,
    sources:
      (result.sources as any[])?.map(
        (row: any) => new ConversationContext(row),
      ) ?? [],
  } as ConversationRecord;
}

export async function insertNewConversationState(
  schema: string,
  conversationState: ConversationState,
): Promise<ConversationState | null> {
  const db = await getDB(schema);
  try {
    const state = await db
      .insertInto('conversation_state')
      .values({
        conversation_id:
          conversationState.conversation_id.toString() as ConversationId,
        metadata_filter: JSON.stringify(
          conversationState.metadata_filter || [],
        ),
        cartridge_enum_value: conversationState.cartridge_enum_value,
      })
      .returning([
        'id',
        'conversation_id',
        'created_date',
        'metadata_filter',
        'cartridge_enum_value',
      ])
      .executeTakeFirstOrThrow();

    return {
      ...state,
      metadata_filter: state.metadata_filter as number[],
      conversation_id: parseInt(state.conversation_id),
    };
  } catch (err) {
    console.error(`[insertNewConversationState] ${err.message}`);
    return null;
  }
}

export async function getConversationState(
  schema: string,
  conversationId: number,
): Promise<ConversationState[]> {
  const queryString = `
    SELECT id, conversation_id, created_date, metadata_filter
    FROM ${schema}.conversation_state
    WHERE conversation_id = $1
    ORDER BY id desc;
  `;
  try {
    const result = await query(queryString, [conversationId]);
    if (result.rows && result.rows.length) {
      return result.rows;
    }
  } catch (err) {
    console.error(`[getConversationState] ${err.message}`);
    return null;
  }
}

export async function updateConversationState(
  schema: string,
  conversationState: ConversationState,
) {
  const queryString = `
    UPDATE ${schema}.conversation_state
    SET metadata_filter = $1
    WHERE id = $2
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [
      JSON.stringify(conversationState.metadata_filter ?? []),
      conversationState.id,
    ]);
    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[updateConversationState] ${err.message}`);
    return null;
  }
}

export async function updateConversationRecord(
  schema: string,
  conversationId: ConversationId,
  sources: ConversationContext[],
  onlyUpdateLastActivityDate: boolean = false,
) {
  try {
    const db = await getDB();
    return db
      .withSchema(schema)
      .updateTable('conversation')
      .set(
        onlyUpdateLastActivityDate
          ? { last_activity_date: new Date() }
          : {
              last_activity_date: new Date(),
              sources: JSON.stringify(sources),
              embedding_ids: sources.map((s) => s.embedding_id),
            },
      )
      .where('id', '=', conversationId)
      .returning(['id'])
      .executeTakeFirstOrThrow();
  } catch (err) {
    // We swallow this error why??
    console.error(`[updateConversationRecord] ${err.message}`);
  }
}

export async function getConversationDetailById(
  schema: string,
  conversation_detail_id: ConversationDetailId,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .innerJoin(
      'conversation',
      'conversation.id',
      'conversation_detail.conversation_id',
    )
    .where('conversation_detail.id', '=', conversation_detail_id)
    .selectAll('conversation_detail')
    .executeTakeFirst();
}

export async function getConversationDetailRecordByIndex(
  schema: string,
  conversation_id: ConversationId,
  index: number = 0,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .innerJoin(
      'conversation',
      'conversation.id',
      'conversation_detail.conversation_id',
    )
    .where('conversation.id', '=', conversation_id)
    .selectAll('conversation_detail')
    .orderBy('id', 'asc')
    .offset(index)
    .executeTakeFirst();
}

//Partial conversation_detail record. So we can get an Id that we can send to call_llm q.
export async function insertPartialConversationDetail(
  schema: string,
  conversationId: ConversationId,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values({ conversation_id: conversationId })
    .returning(['id'])
    .executeTakeFirst();
}

export async function updateConversationDetail(
  schema: string,
  conversationDetailId: ConversationDetailId,
  conversationDetail: ConversationDetailUpdate,
) {
  const db = await getDB();

  return await db
    .withSchema(schema)
    .updateTable('conversation_detail')
    .set({
      conversation_id: conversationDetail.conversation_id,
      message_id: conversationDetail.message_id,
      output: conversationDetail.output,
      statistics: conversationDetail.statistics,
      created_date: conversationDetail.created_date ?? new Date(), // Todo: createed_date should not be nullalbe
      input: conversationDetail.input,
    })
    .where('id', '=', conversationDetailId)
    .returning(['id'])
    .executeTakeFirst();
}

export async function insertConversationDetail(
  schema: string,
  conversationId: ConversationId,
  message: ConversationMessage,
  userId?: number,
  input?: ConversationMessage,
  stats?: Statistics,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'agent',
      conversation_id: conversationId,
      input,
      message,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('conversation_id', '=', conversationId)
        .limit(1),
      statistics: stats ?? {},
      created_date: new Date(),
      user_id: userId,
    }))
    .returning(['id'])
    .executeTakeFirst();
}

// These are special conversation_details that are
// injected by us into the conversation_detail sequence.
// They should be ignored in any analytics or processing.
export async function insertInjectedConversationDetail(
  schema: string,
  conversationId: ConversationId,
  message: ConversationMessage,
  userId?: number,
  input?: ConversationMessage,
  stats?: Statistics,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'agent',
      conversation_id: conversationId,
      input,
      message,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('conversation_id', '=', conversationId)
        .limit(1),
      statistics: stats ?? {},
      created_date: new Date(),
      user_id: userId,
      is_injected_message: true,
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export const addModifiedAnswerToConversationHistory = async (
  schema: string,
  conversationId: ConversationId,
  modifiedText: string,
  userId: number,
  timestampOffsetMs: number = 0,
) => {
  await insertInjectedConversationDetail(
    schema,
    conversationId,
    {
      name: 'Coworker',
      role: 'user',
      content: `A coworker has modified the answer for this question:\n\n${modifiedText}`,
    } as ConversationMessage,
    userId,
  );

  await insertInjectedConversationDetail(
    schema,
    conversationId,
    {
      role: 'assistant',
      content: `Modified answer saved.`,
    } as ConversationMessage,
    userId,
    {
      name: 'Coworker',
      role: 'user',
      content: `A coworker has modified the answer for this question:\n\n${modifiedText}`,
    } as ConversationMessage,
    {
      duration: {
        end: new Date(
          new Date().getTime() + timestampOffsetMs + 100,
        ).toISOString(),
        start: new Date(new Date().getTime() + timestampOffsetMs).toISOString(),
      },
      token_usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      },
    },
  );
};

export async function insertConversationDetailFile(
  schema: string,
  conversationDetailId: ConversationDetailId,
  mimeType: string,
  storageId: string,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail_file')
    .values((eb) => ({
      mime_type: mimeType,
      storage_id: storageId,
      conversation_detail_id: conversationDetailId,
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function insertConversationDetailEmbedding(
  schema: string,
  conversationDetailId: ConversationDetailId,
  records: ConversationContext[],
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail_embedding')
    .values(
      records.map((context) => {
        return {
          conversation_detail_id: conversationDetailId,
          embedding_id: context.embedding_id,
        };
      }),
    )
    .execute();
}

// Create empty conversation_detail record to get an id for the activity log.
export async function createConversationDetail(
  schema: ClientSchema,
  conversationId: ConversationId,
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'agent',
      conversation_id: conversationId,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('conversation_id', '=', conversationId)
        .limit(1),
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function completeConversationDetail(
  schema: ClientSchema,
  conversationDetailId: ConversationDetailId,
  message: ConversationMessage,
  sources: ConversationDetailSources,
  input: any,
  stats?: Statistics,
): Promise<boolean> {
  const db = await getDB();
  const updateValues: Record<string, any> = {
    message,
    statistics: stats,
    created_date: new Date(),
    sources,
  };

  if (input !== null) {
    updateValues.input = input;
  }

  const result = await db
    .withSchema(schema)
    .updateTable('conversation_detail')
    .set(updateValues)
    .where('id', '=', conversationDetailId)
    .returning('id')
    .execute();
  if (result && result.length) {
    return result[0].id === conversationDetailId;
  } else {
    throw new Error(
      `Failed to complete conversation detail, id ${conversationDetailId} not found`,
    );
  }
}

export async function attachConversationDetailEmbeddings(
  schema: ClientSchema,
  conversationDetailId: ConversationDetailId,
  embeddingIds: number[],
): Promise<boolean> {
  if (embeddingIds.length === 0) return false;

  const values = [];
  const placeholders = embeddingIds
    .map((_, index) => `($1, $${index + 2})`)
    .join(', ');

  const queryString = `
    INSERT INTO ${schema}.${constants.TABLE_CONVERSATION_DETAIL_EMBEDDING}
    (conversation_detail_id, embedding_id)
    VALUES ${placeholders}
    ON CONFLICT DO NOTHING;
  `;

  values.push(conversationDetailId, ...embeddingIds);

  const result = await query(queryString, values);
  return result.rowCount > 0;
}

async function getMessagesAfterSeq(
  schema: ClientSchema,
  conversationId: ConversationId,
  seq: number,
): Promise<ConversationMessage[]> {
  const db = await getDB();
  const result = await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .select(['conversation_detail.message'])
    .where('conversation_id', '=', conversationId)
    .where('seq', '>', seq)
    .where('type', '=', 'agent')
    .orderBy('seq', 'asc')
    .execute();
  return result.map((r) => r.message as ConversationMessage);
}

async function getLatestSummaryByConversationId(
  schema: ClientSchema,
  conversationId: ConversationId,
): Promise<ConversationSummary> {
  const db = await getDB();
  return db
    .withSchema(schema)
    .selectFrom('conversation_summary')
    .selectAll()
    .where('conversation_id', '=', conversationId)
    .orderBy('seq', 'desc')
    .executeTakeFirst();
}

export type ConversationHistory = Awaited<
  ReturnType<typeof getActiveConversationHistory>
>;

export async function getActiveConversationHistory(
  schema: ClientSchema,
  conversationId: ConversationId,
) {
  // Step 1: Get the latest summary for the conversation.
  const latestSummary = await getLatestSummaryByConversationId(
    schema,
    conversationId,
  );

  let summaryMessage: ConversationMessage = null,
    lastSummarySeq = 0;

  if (latestSummary) {
    summaryMessage = {
      role: 'assistant',
      content: `Here is a summary of our conversation so far: ${latestSummary.content}`,
    };
    lastSummarySeq = latestSummary.seq;
  }

  // Step 2: Get subsequent messages after the last summary
  const subsequentMessages = await getMessagesAfterSeq(
    schema,
    conversationId,
    lastSummarySeq,
  );

  return {
    seq: lastSummarySeq + subsequentMessages.length,
    messages: subsequentMessages,
    summary: summaryMessage,
  };
}

export async function insertConversationSummary(
  schema: ClientSchema,
  summary: NewConversationSummary,
) {
  const db = await getDB();
  return db
    .withSchema(schema)
    .insertInto('conversation_summary')
    .values(summary)
    .returning('id')
    .executeTakeFirstOrThrow();
}

// Try to acquire a lock on processing this conversation.
export async function tryConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
  lockDurationInSeconds = 120,
): Promise<boolean> {
  // Try to acquire the lock.

  const db = await getDB();
  const result = await db
    .withSchema(schema)
    .updateTable('conversation')
    .set(({ fn }) => ({
      lock_expires_at: sql<Date>`NOW() + ${lockDurationInSeconds} * INTERVAL '1 second'`,
    }))
    .where('id', '=', conversationId)
    .where((eb) =>
      eb('lock_expires_at', 'is', null).or(
        'lock_expires_at',
        '<',
        sql<Date>`NOW()`,
      ),
    )
    .returning('id')
    .execute();

  return result.length > 0; // True if the lock was acquired, false otherwise
}

// Extend a conversation lock that you have already acquired.
export async function extendConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
  lockDurationInSeconds = 120,
) {
  // Extend the lock using PostgreSQL's NOW() function.
  const db = await getDB();
  return await db
    .withSchema(schema)
    .updateTable('conversation')
    .set(({ fn }) => ({
      lock_expires_at: sql<Date>`NOW() + ${lockDurationInSeconds} * INTERVAL '1 second'`,
    }))
    .where('id', '=', conversationId)
    .returning('id')
    .execute();
}

// Release conversation lock if queue is empty. If release fails, there are
// more messages to process.
export async function tryReleaseConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
): Promise<boolean> {
  const client = await getClient();

  try {
    // Start a transaction
    await client.query('BEGIN');

    // Check if there are any queued messages
    const checkQuery = `
      SELECT 1
      FROM ${schema}.${constants.TABLE_CONVERSATION_DETAIL}
      WHERE conversation_id = $1 AND type = 'queued'
      LIMIT 1;
    `;
    const checkRes = await client.query(checkQuery, [conversationId]);

    // If no queued messages, release the lock
    if (checkRes.rows.length === 0) {
      const releaseQuery = `
        UPDATE ${schema}.${constants.TABLE_CONVERSATION}
        SET lock_expires_at = NULL
        WHERE id = $1;
      `;
      await client.query(releaseQuery, [conversationId]);

      // Commit the transaction
      await client.query('COMMIT');
      return true; // Lock released
    } else {
      // There are still queued messages, do not release the lock
      // Rollback the transaction to ensure atomicity
      await client.query('ROLLBACK');
      return false; // Lock not released
    }
  } catch (error) {
    // In case of any error, rollback the transaction
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release(); // Ensure the client is always released back to the pool
  }
}

export async function consumeMessageQueue(
  schema: ClientSchema,
  conversation_id: ConversationId,
): Promise<ConversationMessage[]> {
  // Step 1: Load Queued Messages
  const db = await getDB();
  const messageRows = await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .select(['id', 'message'])
    .where('conversation_id', '=', conversation_id)
    .where('type', '=', 'queued')
    .orderBy('seq', 'asc')
    .execute();

  if (messageRows.length === 0) {
    // Queue is empty.
    return [];
  }

  // After reading them, delete the messages from the queue
  const messageIds = messageRows.map((row) => row.id);
  const messages = messageRows.map((row) => row.message as ConversationMessage);

  if (messageIds.length > 0) {
    await db
      .withSchema(schema)
      .deleteFrom('conversation_detail')
      .where('id', 'in', messageIds)
      .execute();
  }

  return messages;
}

export async function queueConversationMessage(
  schema: ClientSchema,
  conversationId: ConversationId,
  message: ConversationMessage,
) {
  const db = await getDB();
  return db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'queued',
      conversation_id: conversationId,
      message,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('type', '=', 'queued')
        .where('conversation_id', '=', conversationId)
        .limit(1),
      created_date: new Date(),
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function updateConversationSources(
  schema: ClientSchema,
  conversationId: ConversationId,
  sources: ConversationContext[],
) {
  try {
    const queryString = `
    UPDATE ${schema}.${constants.TABLE_CONVERSATION}
    SET sources = $1
    WHERE id = $2
    RETURNING id;
    `;
    const result = await query(queryString, [
      JSON.stringify(sources),
      conversationId,
    ]);

    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[updateConversationSources] ${err.message}`);
  }
}
