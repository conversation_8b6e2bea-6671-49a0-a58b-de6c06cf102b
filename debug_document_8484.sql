-- Comprehensive diagnostic query for document 8484 status
-- This query will help identify why the document shows "processing" status

WITH embedding_counts AS (
  SELECT 
    document_id, 
    COUNT(*) as embedding_count
  FROM c000001.embedding
  GROUP BY document_id
)
SELECT
  -- Document info
  d.id,
  d.file_name,
  d.label,
  d.status as document_status,
  d.job_id,
  d.created_date,
  d.use_for_generation,
  
  -- Job info
  j.id as job_id_check,
  j.status as job_status,
  j.error as job_error,
  j.type as job_type,
  j.created_date as job_created_date,
  j.updated_at as job_updated_at,
  
  -- Document type info
  dt.name as document_type,
  dt.is_website,
  dt.is_rfp,
  
  -- Updated document info (for version tracking)
  d2.id as updated_document_id,
  d2.status as updated_document_status,
  d2.file_name as updated_document_filename,
  d2.created_date as updated_document_created,
  
  -- Embedding info
  COALESCE(ec.embedding_count, 0) as embedding_count,
  
  -- Status logic result (what the getDocs API returns)
  CASE
    WHEN j.status = 'error' AND dt.name = 'Call Transcript' AND COALESCE(ec.embedding_count, 0) > 0 THEN d.status
    WHEN j.status = 'error' THEN j.status
    ELSE d.status
  END AS api_returned_status,
  
  -- UI processing logic (what the FileTableRow component shows)
  CASE
    WHEN d2.id IS NOT NULL AND d2.status = 'processing' THEN 'Updating (UI shows this)'
    WHEN d2.id IS NOT NULL OR d.status IS NULL THEN 'Processing (UI shows this)'
    ELSE CONCAT('Normal: ', UPPER(SUBSTRING(COALESCE(d.status, 'unknown'), 1, 1)), LOWER(SUBSTRING(COALESCE(d.status, 'unknown'), 2)))
  END AS ui_display_status,
  
  -- Diagnostic flags
  CASE WHEN d.status = 'processing' THEN '🔴 Document status is processing' ELSE '✅ Document status is not processing' END as doc_status_check,
  CASE WHEN j.status = 'processing' THEN '🔴 Job status is processing' ELSE '✅ Job status is not processing' END as job_status_check,
  CASE WHEN d2.id IS NOT NULL THEN '🔴 Has updated_document_id (causes Processing in UI)' ELSE '✅ No updated_document_id' END as updated_doc_check,
  CASE WHEN d2.status = 'processing' THEN '🔴 Updated document is processing (causes Updating in UI)' ELSE '✅ Updated document not processing' END as updated_doc_status_check

FROM c000001.document d
LEFT JOIN c000001.job j ON j.id = d.job_id
LEFT JOIN c000001.document_type dt ON dt.id = d.document_type_id
LEFT JOIN c000001.document d2 ON d2.prior_version_document_id = d.id 
  AND d2.status IN ('processing', 'active')
LEFT JOIN embedding_counts ec ON ec.document_id = d.id
WHERE d.id = 8484;

-- Additional queries to check related data:

-- Check if there are any other documents that reference this one
SELECT 'Documents referencing 8484 as prior version:' as info;
SELECT id, file_name, status, created_date 
FROM c000001.document 
WHERE prior_version_document_id = 8484;

-- Check job logs for more details
SELECT 'Job logs for document 8484:' as info;
SELECT jl.id, jl.type, jl.data, jl.created_date
FROM c000001.job_log jl
JOIN c000001.document d ON d.job_id = jl.job_id
WHERE d.id = 8484
ORDER BY jl.created_date DESC
LIMIT 10;

-- Check embeddings
SELECT 'Embedding count for document 8484:' as info;
SELECT COUNT(*) as embedding_count, MIN(id) as first_embedding, MAX(id) as last_embedding
FROM c000001.embedding 
WHERE document_id = 8484;
