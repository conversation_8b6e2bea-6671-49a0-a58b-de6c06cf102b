// Changes: Modified upsertMessage to detect learn_a_fact blocks and send them as tool_completion_blocks
// This enables webchat to receive interactive UI blocks from the learn_a_fact tool
// Following the same pattern as feedback buttons and hint pills implementation
// Added processFilesForWebChat to handle PPTX and other file downloads in webchat

import {
  findMarkdownImages,
  generateTimestampRandomId,
  LOG_LEVEL,
  LOG_TOGGLE_NAME,
} from '@tribble/common-utils';
import {
  constants,
  ConversationRecord,
  ConversationState,
  getConversationById,
  getConversationByMessageIdAndChannel,
  getConversationDetailById,
  getConversationState,
  getMetadataFilterRecords,
  getSecret,
  getSettings,
  insertNewConversation,
  insertNewConversationState,
  logDebugMessage,
  processFilesForWebChat,
  processImagesForWebChat,
  System,
  updateConversationRecord,
} from '@tribble/comms-shared';
import conversationServiceHandler, {
  WebChatMiddlewareArgs,
} from '@tribble/comms-shared/dist/handler/agentHandler';
import {
  DeleteMessageArgs,
  FileUpload,
  UpsertMessageArgs,
  UpsertMessageResponse,
} from '@tribble/comms-shared/dist/handler/unifiedMiddleware/sendMessages';
import { convertSlackTextToWeb } from '@tribble/comms-shared/dist/handler/unifiedMiddleware/sendMessages/slackToTeamsUtils';
import { getPrivacyWarningForSources } from '@tribble/comms-shared/dist/handler/utils/getPrivacyWarningMessage';
import { ClientSchema } from '@tribble/tribble-db';
import { ConversationEventType as EventType } from '@tribble/types-shared';

import { ChatMessage, CustomWebSocket } from '.';
import { getLogger } from '../utils/logger';
import {
  ActionsBlock,
  EphemeralUIMetadata,
  Message as OAIMessage,
  UIBlock,
} from './types';
import { generateHintPillsForWebChat } from './webChatHintPills';

export async function handleWebChatMessage({
  message,
  ws,
}: {
  message: {
    text: string;
    files?: any[];
    ts: string;
  };
  ws: CustomWebSocket;
}) {
  const logger = getLogger();
  const log = (
    message: string,
    toggleName: LOG_TOGGLE_NAME = LOG_TOGGLE_NAME.SYSTEM,
    level: LOG_LEVEL = LOG_LEVEL.INFO,
    obj: Record<string, any> | null = null,
    additionalContext: Record<string, any> = {},
  ): boolean => {
    return logger.log(
      message,
      toggleName,
      level,
      obj,
      user.client.database_schema_id,
      additionalContext,
    );
  };
  const params = {
    context: {
      conversationRecord: null,
      conversationStateRecord: null,
      logger,
      log,
    },
  } as unknown as WebChatMiddlewareArgs;
  const user = ws.user.userDetail;

  // Websocket event type defaults to WEBCHAT_MESSAGE
  const eventType = ws.event_type ?? EventType.WEBCHAT_MESSAGE;

  logDebugMessage(
    `[handleWebChatMessage] Got a message: ${JSON.stringify(message, null, 2)}`,
  );

  params.next = () => Promise.resolve();
  params.system = System.WebChat;

  // user and client check
  params.tribbleUser = user;
  params.client = user.client;
  params.platform_user_id = user.id;
  const schema = user.client.database_schema_id as ClientSchema;

  if (message.text === '' && message.files && message.files.length === 0) {
    ws.sendMessage({
      type: 'message',
      message: {
        text: 'Empty messages are no way to talk to a bot. Try asking a question instead.',
        role: 'assistant',
        ts: generateTimestampRandomId(),
        view: [],
      },
    });
    return;
  }

  params.upsertMessage = async ({
    id,
    text,
    blocks,
    messageType,
    conversationDetailId,
  }: UpsertMessageArgs): Promise<UpsertMessageResponse> => {
    const upsertMessage: ChatMessage = {
      text: convertSlackTextToWeb(text),
      view: blocks,
      role: 'assistant',
      messageType,
      conversationId: parseInt(conversationRecord.id),
      ts: id || generateTimestampRandomId(),
      ...(conversationDetailId && { conversationDetailId }),
    };

    // Check if blocks contain ephemeral UI or learn_a_fact actions
    if (blocks && blocks.length > 0) {
      const typedBlocks = blocks as UIBlock[];

      // Check for ephemeral UI blocks (from generate_ephemeral_ui or generate_result_ui)
      const hasEphemeralUIActions = typedBlocks.some(
        (block) =>
          block.type === 'actions' &&
          (block as ActionsBlock).elements?.some(
            (element) =>
              element.action_id === 'ephemeral_ui_submit' ||
              element.action_id === 'ephemeral_ui_cancel',
          ),
      );

      // Check for learn_a_fact actions (existing functionality)
      const hasLearnFactActions = typedBlocks.some(
        (block) =>
          block.type === 'actions' &&
          (block as ActionsBlock).elements?.some(
            (element) =>
              element.action_id === 'add_to_brain_accept_id' ||
              element.action_id === 'add_to_brain_change_id' ||
              element.action_id === 'add_to_brain_dismiss_id',
          ),
      );

      const uiOutputTypes: UIBlock['type'][] = [
        'divider',
        'context',
        'section',
        'divider',
      ];

      const hasEphemeralUIOutput = false;
      //Temporarily disable this
      // const hasEphemeralUIOutput =
      //   !hasLearnFactActions &&
      //   typedBlocks.some((block) => uiOutputTypes.includes(block.type));

      if (hasEphemeralUIActions) {
        // Extract UI metadata from the action value
        const actionBlock = typedBlocks.find(
          (block) => block.type === 'actions',
        ) as ActionsBlock;
        if (actionBlock && actionBlock.elements?.[0]?.value) {
          try {
            const actionValue = JSON.parse(
              actionBlock.elements[0].value,
            ) as EphemeralUIMetadata;

            // Send as ephemeral_ui_blocks for generic UI handling
            ws.sendMessage({
              type: 'ephemeral_ui_blocks',
              ts: upsertMessage.ts,
              blocks: blocks,
              uiId: actionValue.ui_id,
              uiPurpose: actionValue.purpose || 'form',
              conversationDetailId:
                conversationDetailId || actionValue.conversation_detail_id,
            });

            return {
              id: upsertMessage.ts,
              ok: true,
              message: {
                text,
                id: upsertMessage.ts,
                channel,
                from: 'bot',
                to: user.id,
                isDM: true,
                channelType: 'personal-chat',
                threadId: undefined,
                files: [],
                event: eventType ?? EventType.WEBCHAT_MESSAGE,
              },
            };
          } catch (err) {
            console.error(
              '[handleWebChatMessage] Error parsing ephemeral UI action value:',
              err,
            );
          }
        }
      } else if (hasLearnFactActions) {
        // Existing learn_a_fact handling
        const actionBlock = blocks.find(
          (block: any) => block.type === 'actions',
        ) as any;
        if (actionBlock && actionBlock.elements?.[0]?.value) {
          try {
            const actionValue = JSON.parse(actionBlock.elements[0].value);

            // Send as tool_completion_blocks instead of regular message
            ws.sendMessage({
              type: 'tool_completion_blocks',
              ts: upsertMessage.ts,
              blocks: blocks,
              toolCallId: actionValue.tool_call_id,
              conversationDetailId: actionValue.conversation_detail_id,
            });

            return {
              id: upsertMessage.ts,
              ok: true,
              message: {
                text,
                id: upsertMessage.ts,
                channel,
                from: 'bot',
                to: user.id,
                isDM: true,
                channelType: 'personal-chat',
                threadId: undefined,
                files: [],
                event: eventType ?? EventType.WEBCHAT_MESSAGE,
              },
            };
          } catch (err) {
            console.error(
              '[handleWebChatMessage] Error parsing action value:',
              err,
            );
          }
        }
      } else if (hasEphemeralUIOutput) {
        // If the blocks contain output UI elements, send them as ephemeral_ui_blocks
        ws.sendMessage({
          type: 'ephemeral_ui_blocks',
          ts: upsertMessage.ts,
          blocks: blocks,
          conversationDetailId,
        });

        return {
          id: upsertMessage.ts,
          ok: true,
          message: {
            text,
            id: upsertMessage.ts,
            channel,
            from: 'bot',
            to: user.id,
            isDM: true,
            channelType: 'personal-chat',
            threadId: undefined,
            files: [],
            event: eventType ?? EventType.WEBCHAT_MESSAGE,
          },
        };
      }
    }

    // Check for markdown images
    const matches = findMarkdownImages(upsertMessage.text);
    if (matches.length > 0) {
      // The image is already sitting in blob storage, but it's private.
      // Construct a hashed image URL that we can return to the frontend.
      // A new /images/{hashed_id} route will be created to serve these images.
      const processedMessage = await processImagesForWebChat(
        upsertMessage.text,
        user.id,
        user.client.id,
        matches,
      );

      upsertMessage.text = processedMessage;
    }

    ws.sendMessage({
      type: 'message',
      message: upsertMessage,
    });

    return {
      id: upsertMessage.ts,
      ok: true,
      message: {
        text,
        id: upsertMessage.ts,
        channel,
        from: 'bot',
        to: user.id,
        isDM: true,
        channelType: 'personal-chat',
        threadId: undefined,
        files: [],
        event: eventType ?? EventType.WEBCHAT_MESSAGE,
      },
    };
  };
  params.postMessageUserOnly = async () => {};
  params.deleteMessage = async ({ id }: DeleteMessageArgs) => {
    ws.sendMessage({
      type: 'delete',
      id,
    });
    return { ok: true };
  };

  params.completeMessage = async ({ id, conversationDetailId, ...args }) => {
    try {
      // Query for any used sources using the conversation detail id
      const conversationDetail = await getConversationDetailById(
        schema,
        conversationDetailId,
      );
      let message = conversationDetail.message as OAIMessage;
      const isObject = message && typeof message === 'object';
      const hasToolCalls =
        isObject && message?.tool_calls && message.tool_calls.length > 0;
      const hasContent =
        isObject && message?.content && message.content.length > 0;
      const isHyrid = hasToolCalls && hasContent;

      const shouldSendHintPills = !isHyrid;
      const shouldSendFeedbackButtons = !isHyrid;

      // Process the agent's message for generated images FIRST
      if (message && typeof message === 'object' && 'content' in message) {
        let messageContent = message.content;
        if (messageContent) {
          const imageMatches = findMarkdownImages(messageContent);
          if (imageMatches.length > 0) {
            messageContent = await processImagesForWebChat(
              messageContent,
              user.id,
              user.client.id,
              imageMatches,
            );

            // Send the processed message with viewable images
            ws.sendMessage({
              type: 'update',
              message: {
                text: messageContent,
                role: 'assistant',
                ts: id,
                view: [],
                conversationId: parseInt(conversationRecord.id),
              },
            });
          }

          // Process file links (PPTX, PDF, etc.) for secure downloads
          const fileProcessedMessage =
            await processFilesForWebChat(messageContent);

          if (fileProcessedMessage !== messageContent) {
            // File links were found and processed
            ws.sendMessage({
              type: 'update',
              message: {
                text: fileProcessedMessage,
                role: 'assistant',
                ts: id,
                view: [],
                conversationId: parseInt(conversationRecord.id),
              },
            });
          }
        }
      }

      const usedSources = conversationDetail.sources as {
        brain: any[];
        web: any[];
        structured: any[];
      };

      const hasNoSources =
        !usedSources ||
        (usedSources.brain?.length === 0 &&
          usedSources.web?.length === 0 &&
          usedSources.structured?.length === 0);

      if (!hasNoSources) {
        // Keep track so don't dupe
        const citedSource = {};

        // Check for privacy warnings
        const brainSources = usedSources.brain || [];
        const privacyWarning =
          brainSources.length > 0
            ? await getPrivacyWarningForSources(schema, brainSources)
            : '';

        // If a message is complete, build up the Sources "footer"
        let messageText = '\n\n**Sources**';

        usedSources.brain.forEach((source) => {
          if (user.no_admin_console === true) {
            if (!citedSource[source.file_name]) {
              // If user doesn't have access to the admin console, don't show brain URL/reference
              // that point to internal sources
              const showUrl =
                source.url && !source.url.startsWith(process.env.APP_PATH);

              if (showUrl) {
                messageText += `\n- [${source.file_name}](${source.url})`;
              } else {
                messageText += `\n- ${source.file_name}`;
              }

              citedSource[source.file_name] = true;
            }
          } else {
            if (source.url) {
              if (!citedSource[source.file_name + source.url]) {
                // Check if source reference is a YYYY-MM-DD date.
                // If so, it's likely an ingest date, which isn't very useful.
                // (And sometimes not representative of the underlying content.)
                const datePattern =
                  /^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])$/;
                const isDateReference = datePattern.test(source.reference);

                if (source.reference && !isDateReference) {
                  messageText += `\n- [${source.file_name} (${source.reference})](${source.url})`;
                } else {
                  messageText += `\n- [${source.file_name}](${source.url})`;
                }
                citedSource[source.file_name + source.url] = true;
              }
            } else {
              if (!citedSource[source.file_name + source.reference]) {
                messageText += `\n- ${source.file_name} (${source.reference})`;
                citedSource[source.file_name + source.reference] = true;
              }
            }
          }
        });

        usedSources.web.forEach((source) => {
          if (!citedSource[source.file_name + source.url]) {
            messageText += `\n- [${source.file_name}](${source.url})`;
            citedSource[source.file_name + source.url] = true;
          }
        });

        usedSources.structured.forEach((source) => {
          messageText += `\n- [${source.file_name}](${source.url})`;
          citedSource[source.file_name + source.url] = true;
        });

        // Append sources to message
        ws.sendMessage({
          type: 'append',
          message: {
            text: messageText,
            role: 'assistant',
            ts: id,
            view: [],
            conversationId: parseInt(conversationRecord.id),
          },
        });

        // Send privacy warning if exists
        if (privacyWarning) {
          ws.sendMessage({
            type: 'privacy_warning',
            ts: id,
            warning: privacyWarning,
          });
        }
      }

      // Send stream complete signal
      ws.sendMessage({
        type: 'stream_complete',
        ts: id,
      });

      // Send active metadata filters (tags)
      if (
        params.context.activeMetadataFilters &&
        params.context.activeMetadataFilters.length > 0
      ) {
        ws.sendMessage({
          type: 'metadata_filters',
          ts: id,
          filters: params.context.activeMetadataFilters.map((filter) => ({
            id: filter.id,
            name: filter.name,
            type: filter.type.name,
            icon: filter.type.icon,
          })),
        });
      }

      // Add feedback buttons
      if (shouldSendFeedbackButtons) {
        ws.sendMessage({
          type: 'feedback_buttons',
          ts: id,
          conversationDetailId,
          conversationId: parseInt(conversationRecord.id),
        });
      }

      // Generate and send hint pills
      if (shouldSendHintPills) {
        const hints = await generateHintPillsForWebChat({
          schema,
          userId: user.id,
          userMessage: params.message.text,
          clientId: user.client.id,
          isFollowUp: false, // WebChat doesn't have threading yet
          conversationDetailId,
        });

        if (hints.length > 0) {
          ws.sendMessage({
            type: 'hint_pills',
            ts: id,
            hints,
          });
        }
      }

      return { ok: true };
    } catch (err) {
      console.error('[handleWebChatMessage] Error completing message', err);
      return { ok: false };
    }
  };

  params.context.botMentioned = false;

  params.context.settings = await getSettings(user, user.client);

  params.context.helperFuncKey = await getSecret(
    constants.SECRET_HELPER_FUNC_KEY,
  );

  const allMetadataFilters = await getMetadataFilterRecords(schema);
  params.context.allMetadataFilters = allMetadataFilters;

  // This should not be set -- or rather, only if/when we implement the metadata tag
  // inference feature (same as in the Slack).
  // In any case, this should NOT be set to all active MDF.

  let activeMetadataFilters = []; // allMetadataFilters.filter((mdf) => mdf.is_active);

  // TODO: add file handling
  params.uploadFile = async (params: FileUpload) => {
    const fileId = generateTimestampRandomId();

    // Send file upload status to frontend
    ws.sendMessage({
      type: 'file_upload_status',
      fileId,
      status: 'received',
      fileName: params.filename,
      fileType: params.filetype,
    });

    // TODO: Implement actual file processing
    // For now, just acknowledge receipt
    console.log('[handleWebChatMessage] File upload received:', {
      fileName: params.filename,
      mimeType: params.filetype,
      size: params.file.length,
    });

    return {
      id: fileId,
      ok: true,
    };
  };

  // TODO: add verbatim mode

  // TODO: add inference tags

  // TOOD: if conversation is not found and context_type and context_id are provided, create a new custom conversation like
  // Tim did here: https://github.com/tribble-ai/ds9/pull/1080/files#diff-db9befcc5b55e3daa8abef367de2b0e3602468cb4e2013b7df6b59509b4b78f4

  let conversationRecord: ConversationRecord | null;
  let conversationStateRecord: ConversationState | null;
  if (ws.conversation_id) {
    conversationRecord = await getConversationById(
      user.client.database_schema_id,
      ws.conversation_id,
    );
    if (!conversationRecord) {
      ws.close(4000, 'Conversation not found');
      return;
    }
  } else if (ws.context_type && ws.context_id) {
    conversationRecord = await getConversationByMessageIdAndChannel(
      schema,
      `${ws.context_type}.${ws.context_id}`,
      message.ts,
    );
  }

  if (conversationRecord) {
    const stateRecords = await getConversationState(
      user.client.database_schema_id,
      conversationRecord.id as unknown as number,
    );
    conversationStateRecord = stateRecords?.[0];

    await updateConversationRecord(schema, conversationRecord.id, null, true);
  } else {
    params.context.runEagerRag = true;

    conversationRecord = await insertNewConversation(schema, {
      channel:
        ws.context_type && ws.context_id
          ? `${ws.context_type}.${ws.context_id}`
          : 'web_chat',
      system: System.WebChat,
      message_id: message.ts,
      user_id: user.id,
    });

    // If starting a new conversation, check the user profile MDF
    activeMetadataFilters = params.context.settings?.defaultTags ?? [];

    conversationStateRecord = await insertNewConversationState(schema, {
      conversation_id: parseInt(conversationRecord.id),
      metadata_filter: activeMetadataFilters ?? [],
    });
  }
  ws.conversation_id = conversationRecord.id;
  params.context.conversationRecord = conversationRecord;
  params.context.conversationStateRecord = conversationStateRecord;
  params.context.activeMetadataFilters = activeMetadataFilters
    ? allMetadataFilters.filter((mdf) => activeMetadataFilters.includes(mdf.id))
    : [];

  const channel = conversationRecord.channel;

  params.message = {
    text: message.text,
    id: message.ts,
    channel,
    from: user.id,
    to: 'bot',
    isDM: true, // TODO: allow muiltiple users in one webchat
    channelType: 'personal-chat',
    threadId: undefined,
    files: message.files,
    event: eventType ?? EventType.WEBCHAT_MESSAGE,
  };

  // Pass cartridge information if available
  if (ws.cartridge) {
    params.cartridgeOptions = {
      cartridgeName: ws.cartridge,
    };
  }

  conversationServiceHandler(params);
}
